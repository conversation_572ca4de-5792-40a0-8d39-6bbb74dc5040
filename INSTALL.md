# 🚀 Установка Content Monster GPT Bot

## 📋 Требования

- Python 3.9+ (рекомендуется 3.11+)
- Windows/Linux/macOS
- Интернет-соединение

## 🔧 Пошаговая установка

### 1. Клонирование/скачивание проекта

```bash
# Если у вас есть git
git clone <repository_url>
cd content-monster-gpt

# Или просто скачайте и распакуйте архив
```

### 2. Установка зависимостей

```bash
# Установка основных зависимостей
pip install aiogram pyrogram sqlalchemy alembic aiosqlite
pip install aiohttp httpx python-dotenv pydantic pydantic-settings
pip install apscheduler loguru python-dateutil asyncio-throttle

# Или из файла requirements.txt (может потребовать компилятор для tgcrypto)
pip install -r requirements.txt
```

**Примечание:** `tgcrypto` и `pillow` опциональны и требуют компилятор C++. Бот работает без них.

### 3. Настройка конфигурации

Скопируйте файл с примером:
```bash
copy .env.example .env    # Windows
cp .env.example .env      # Linux/macOS
```

Отредактируйте `.env` файл:

```env
# Telegram Bot API (получить у @BotFather)
BOT_TOKEN=1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA

# Telegram Userbot API (получить на my.telegram.org)
API_ID=12345678
API_HASH=abcdef1234567890abcdef1234567890

# OpenRouter API (получить на openrouter.ai)
OPENROUTER_API_KEY=sk-or-v1-abcdef1234567890abcdef1234567890

# Ваш Telegram User ID (получить у @userinfobot)
ADMIN_ID=123456789
```

### 4. Получение токенов и ключей

#### 🤖 Telegram Bot Token
1. Напишите [@BotFather](https://t.me/BotFather)
2. Отправьте `/newbot`
3. Следуйте инструкциям
4. Скопируйте токен в формате `1234567890:AAAA...`

#### 🔑 API_ID и API_HASH
1. Перейдите на [my.telegram.org](https://my.telegram.org)
2. Войдите в свой аккаунт Telegram
3. Перейдите в "API development tools"
4. Создайте новое приложение
5. Скопируйте `api_id` (число) и `api_hash` (строка)

#### 🧠 OpenRouter API Key
1. Зарегистрируйтесь на [OpenRouter.ai](https://openrouter.ai)
2. Пополните баланс ($5-10 достаточно для начала)
3. Перейдите в "Keys" и создайте новый ключ
4. Скопируйте ключ в формате `sk-or-v1-...`

#### 👤 Admin ID
1. Напишите [@userinfobot](https://t.me/userinfobot)
2. Скопируйте ваш User ID (число)

### 5. Настройка каналов

#### Для каналов-доноров:
- Ваш аккаунт должен быть подписан на канал
- Канал должен быть публичным или вы должны иметь к нему доступ

#### Для целевых каналов:
- Добавьте бота в администраторы канала
- Дайте права на публикацию сообщений

## ▶️ Запуск

```bash
python main.py
```

При первом запуске:
1. Pyrogram попросит ввести номер телефона
2. Введите код подтверждения из Telegram
3. Если включена двухфакторная аутентификация, введите пароль

## ✅ Проверка работы

После запуска вы должны увидеть:
```
🚀 Запуск Content Monster GPT Bot...
✅ Content Monster GPT Bot успешно запущен!
👤 Админ ID: 123456789
🧠 ИИ модель: openai/gpt-4-turbo-preview
📊 Максимум каналов-доноров: 5
```

Напишите боту `/start` - он должен ответить приветствием.

## 🐛 Устранение проблем

### Ошибка "API недоступен"
- Проверьте интернет-соединение
- Убедитесь в правильности API ключей
- Проверьте баланс на OpenRouter

### Ошибка "Userbot не может читать канал"
- Убедитесь, что подписаны на канал
- Проверьте правильность API_ID и API_HASH
- Канал может быть приватным

### Ошибка "Бот не может публиковать"
- Добавьте бота в администраторы канала
- Дайте права на публикацию сообщений
- Проверьте правильность BOT_TOKEN

### Ошибка установки tgcrypto
```bash
# Установите без tgcrypto (бот будет работать медленнее, но стабильно)
pip install aiogram pyrogram sqlalchemy alembic aiosqlite aiohttp httpx python-dotenv pydantic pydantic-settings apscheduler loguru python-dateutil asyncio-throttle
```

### Ошибка установки pillow
```bash
# Установите без pillow (обработка изображений будет недоступна)
# Основная функциональность не пострадает
```

## 🔄 Обновление

```bash
git pull  # Если используете git
# Или скачайте новую версию

# Обновите зависимости
pip install -r requirements.txt --upgrade
```

## 🐳 Docker (опционально)

```bash
# Сборка образа
docker build -t content-monster-gpt .

# Запуск
docker-compose up -d
```

## 📁 Структура файлов

```
content_monster_bot/
├── .env                    # Конфигурация (создать из .env.example)
├── main.py                 # Точка входа
├── requirements.txt        # Зависимости
├── content_monster.db      # База данных (создается автоматически)
├── logs/                   # Логи
├── config/                 # Настройки
├── core/                   # Основные компоненты
├── database/               # Модели БД
├── handlers/               # Обработчики
└── utils/                  # Утилиты
```

## 🆘 Поддержка

Если возникли проблемы:
1. Проверьте логи в папке `logs/`
2. Убедитесь в правильности настроек `.env`
3. Проверьте права бота и доступы userbot
4. Убедитесь в наличии средств на балансе OpenRouter

---

**Готово!** Ваш Content Monster GPT Bot готов к работе! 🎉
