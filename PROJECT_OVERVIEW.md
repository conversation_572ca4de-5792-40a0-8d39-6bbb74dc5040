# 🤖 Content Monster GPT Bot - Обзор проекта

## 📋 Описание

**Content Monster GPT Bot** - это продвинутый Telegram-граббер для автоматического копирования, переписывания и публикации контента из каналов-доноров в ваши каналы с использованием ИИ.

## 🏗️ Архитектура

### Основные компоненты:

1. **Userbot (Pyrogram)** - Чтение постов из каналов-доноров
2. **Telegram Bot (aiogram)** - Управление и публикация
3. **OpenRouter API** - ИИ переписывание текстов
4. **SQLite/PostgreSQL** - Хранение данных
5. **Планировщик (APScheduler)** - Автоматическая публикация

### Схема работы:
```
Каналы-доноры → Userbot → Фильтрация → ИИ обработка → Модерация → Публикация
```

## 📁 Структура проекта

```
content_monster_bot/
├── 📄 main.py                 # Точка входа
├── 📄 start.py                # Быстрый запуск
├── 📄 requirements.txt        # Зависимости
├── 📄 .env.example           # Пример конфигурации
├── 📄 Dockerfile             # Docker образ
├── 📄 docker-compose.yml     # Docker Compose
├── 📄 README.md              # Основная документация
├── 📄 INSTALL.md             # Инструкция по установке
├── 📄 EXAMPLES.md            # Примеры использования
├── 📄 FAQ.md                 # Часто задаваемые вопросы
├── 📄 PROJECT_OVERVIEW.md    # Этот файл
│
├── 📂 config/                # Конфигурация
│   ├── __init__.py
│   └── settings.py           # Настройки приложения
│
├── 📂 core/                  # Основные компоненты
│   ├── __init__.py
│   ├── userbot.py           # Pyrogram userbot
│   ├── telegram_bot.py      # aiogram bot
│   ├── openrouter_api.py    # OpenRouter интеграция
│   └── scheduler.py         # Планировщик задач
│
├── 📂 database/             # База данных
│   ├── __init__.py
│   ├── models.py           # SQLAlchemy модели
│   ├── crud.py             # CRUD операции
│   └── database.py         # Настройка БД
│
├── 📂 handlers/             # Обработчики
│   ├── __init__.py
│   ├── admin_handlers.py   # Админ функции
│   ├── moderation.py       # Модерация постов
│   └── settings_handlers.py # Настройки
│
├── 📂 utils/                # Утилиты
│   ├── __init__.py
│   ├── filters.py          # Фильтры контента
│   ├── media_handler.py    # Обработка медиа
│   └── logger.py           # Логирование
│
└── 📂 logs/                 # Логи (создается автоматически)
    ├── content_monster.log  # Основные логи
    └── errors.log          # Ошибки
```

## 🔧 Технологический стек

### Backend:
- **Python 3.9+** - Основной язык
- **asyncio** - Асинхронное программирование
- **SQLAlchemy** - ORM для работы с БД
- **Alembic** - Миграции БД

### Telegram:
- **aiogram 3.x** - Telegram Bot API
- **Pyrogram 2.x** - Telegram Userbot API
- **tgcrypto** - Ускорение шифрования (опционально)

### ИИ и API:
- **OpenRouter** - Доступ к различным ИИ моделям
- **aiohttp/httpx** - HTTP клиенты
- **pydantic** - Валидация данных

### Планирование:
- **APScheduler** - Планировщик задач
- **python-dateutil** - Работа с датами

### Логирование:
- **loguru** - Продвинутое логирование

### Дополнительно:
- **python-dotenv** - Переменные окружения
- **pillow** - Обработка изображений (опционально)

## 🚀 Возможности

### ✨ Основные функции:
- ✅ Мониторинг до 5 каналов-доноров
- ✅ ИИ переписывание текстов
- ✅ Поддержка всех типов медиа
- ✅ Ручная модерация постов
- ✅ Автоматическая публикация по расписанию
- ✅ Черный список слов
- ✅ Настраиваемые подписи
- ✅ Подробная статистика

### 🎛️ Управление:
- 📱 Удобный Telegram интерфейс
- ⚙️ Гибкие настройки ИИ
- 📊 Детальная аналитика
- 🔄 Автоматическое расписание
- 📝 Кастомные промпты

### 🛡️ Безопасность:
- 🔐 Доступ только для админа
- 📋 Подробное логирование
- 🚫 Фильтрация контента
- ⚡ Защита от спама

## 📊 Поддерживаемые ИИ модели

### OpenRouter модели:
- **OpenAI GPT-4** - Лучшее качество
- **OpenAI GPT-3.5** - Быстро и дешево
- **Anthropic Claude-3** - Хорошо для творчества
- **Mistral 7B** - Бюджетный вариант
- **Meta LLaMA** - Open source альтернатива

### Настройки ИИ:
- 🌡️ **Температура** (0.0-2.0) - Креативность
- 📏 **Макс. токены** (100-4000) - Длина ответа
- 📝 **Промпты** - Стиль переписывания

## 💾 База данных

### Таблицы:
- **donor_channels** - Каналы-доноры
- **target_channels** - Целевые каналы
- **posts** - Посты и их статусы
- **blacklist_words** - Черный список
- **bot_settings** - Настройки бота
- **log_entries** - Логи событий

### Поддерживаемые БД:
- ✅ **SQLite** - По умолчанию
- ✅ **PostgreSQL** - Для продакшена
- ✅ **MySQL** - Альтернатива

## 🔄 Жизненный цикл поста

1. **Мониторинг** - Userbot отслеживает новые посты
2. **Фильтрация** - Проверка черного списка и качества
3. **ИИ обработка** - Переписывание текста
4. **Модерация** - Ручное одобрение/редактирование
5. **Планирование** - Добавление в очередь публикации
6. **Публикация** - Отправка в целевой канал
7. **Логирование** - Запись результата

## 📈 Производительность

### Ограничения:
- **Каналы-доноры:** До 5 (настраивается)
- **Целевые каналы:** Неограниченно
- **Посты в час:** До 20 (настраивается)
- **Размер медиа:** До 50 MB (настраивается)

### Оптимизация:
- ⚡ Асинхронная обработка
- 🔄 Пакетная обработка медиа
- 📊 Кэширование настроек
- 🚀 Параллельная работа компонентов

## 🐳 Развертывание

### Локальный запуск:
```bash
python start.py
```

### Docker:
```bash
docker-compose up -d
```

### Облачные платформы:
- ✅ **Heroku** - Простое развертывание
- ✅ **DigitalOcean** - VPS хостинг
- ✅ **AWS/GCP** - Облачные сервисы
- ✅ **VPS** - Любой Linux сервер

## 💰 Стоимость использования

### OpenRouter API:
- **GPT-3.5:** ~$5-10/месяц (средний канал)
- **GPT-4:** ~$20-50/месяц (средний канал)
- **Claude-3:** ~$10-30/месяц (средний канал)

### Хостинг:
- **Локально:** Бесплатно
- **VPS:** $5-20/месяц
- **Облако:** $10-50/месяц

## 🔮 Планы развития

### Ближайшие обновления:
- 🌐 Веб-интерфейс управления
- 📱 Мобильное приложение
- 🔄 Интеграция с другими ИИ
- 📊 Расширенная аналитика

### Долгосрочные планы:
- 🤖 Собственная ИИ модель
- 🌍 Мультиязычная поддержка
- 📈 Машинное обучение для фильтрации
- 🔗 Интеграция с другими платформами

## 📞 Поддержка

### Документация:
- 📖 **README.md** - Основная информация
- 🚀 **INSTALL.md** - Установка и настройка
- 📚 **EXAMPLES.md** - Примеры использования
- ❓ **FAQ.md** - Часто задаваемые вопросы

### Помощь:
- 📋 Проверьте логи в папке `logs/`
- 🔧 Убедитесь в правильности настроек
- 💬 Создайте issue в репозитории
- 📧 Обратитесь к разработчикам

---

**Content Monster GPT Bot** - мощный инструмент для автоматизации контент-маркетинга в Telegram! 🚀
