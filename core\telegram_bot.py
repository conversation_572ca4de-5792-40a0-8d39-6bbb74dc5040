"""
Telegram Bot для управления и публикации постов
"""
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime

from aiogram import Bo<PERSON>, Dispatcher, F
from aiogram.types import (
    Message, CallbackQuery, InlineKeyboardMarkup,
    InlineKeyboardButton, InputMediaPhoto, InputMediaVideo,
    InputMediaAudio, InputMediaDocument, InputMediaAnimation
)
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.storage.memory import MemoryStorage
from loguru import logger

from config.settings import settings
from database.crud import (
    PostCRUD, DonorChannelCRUD, TargetChannelCRUD,
    BlacklistCRUD, SettingsCRUD, LogCRUD
)
from utils.filters import content_filter


class AdminStates(StatesGroup):
    """Состояния для админ-панели"""
    waiting_for_channel = State()
    waiting_for_blacklist_word = State()
    waiting_for_signature = State()
    waiting_for_post_edit = State()
    waiting_for_target_channel = State()


class ContentMonsterBot:
    """Основной класс Telegram бота"""

    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
        self.bot = Bot(token=settings.bot_token)
        self.dp = Dispatcher(storage=MemoryStorage())
        self.is_running = False

        # Регистрируем обработчики
        self._register_handlers()

    def _register_handlers(self):
        """Регистрация обработчиков команд и коллбэков"""

        # Команды
        self.dp.message.register(self.cmd_start, Command("start"))
        self.dp.message.register(self.cmd_menu, Command("menu"))
        self.dp.message.register(self.cmd_status, Command("status"))
        self.dp.message.register(self.cmd_logs, Command("logs"))

        # Коллбэки главного меню
        self.dp.callback_query.register(self.cb_main_menu, F.data == "main_menu")
        self.dp.callback_query.register(self.cb_donor_channels, F.data == "donor_channels")
        self.dp.callback_query.register(self.cb_target_channels, F.data == "target_channels")
        self.dp.callback_query.register(self.cb_moderation, F.data == "moderation")
        self.dp.callback_query.register(self.cb_settings, F.data == "settings")
        self.dp.callback_query.register(self.cb_blacklist, F.data == "blacklist")

        # Коллбэки управления каналами
        self.dp.callback_query.register(self.cb_add_donor, F.data == "add_donor")
        self.dp.callback_query.register(self.cb_remove_donor, F.data.startswith("remove_donor:"))
        self.dp.callback_query.register(self.cb_add_target, F.data == "add_target")

        # Коллбэки модерации
        self.dp.callback_query.register(self.cb_approve_post, F.data.startswith("approve:"))
        self.dp.callback_query.register(self.cb_reject_post, F.data.startswith("reject:"))
        self.dp.callback_query.register(self.cb_edit_post, F.data.startswith("edit:"))
        self.dp.callback_query.register(self.cb_publish_post, F.data.startswith("publish:"))

        # Коллбэки настроек
        self.dp.callback_query.register(self.cb_change_signature, F.data == "change_signature")
        self.dp.callback_query.register(self.cb_ai_settings, F.data == "ai_settings")
        self.dp.callback_query.register(self.cb_schedule_settings, F.data == "schedule_settings")
        self.dp.callback_query.register(self.cb_statistics, F.data == "statistics")

        # Коллбэки статистики
        self.dp.callback_query.register(self.cb_stats_general, F.data == "stats_general")
        self.dp.callback_query.register(self.cb_stats_posts, F.data == "stats_posts")
        self.dp.callback_query.register(self.cb_stats_today, F.data == "stats_today")
        self.dp.callback_query.register(self.cb_stats_week, F.data == "stats_week")

        # Коллбэки настроек ИИ
        self.dp.callback_query.register(self.cb_ai_change_model, F.data == "ai_change_model")
        self.dp.callback_query.register(self.cb_ai_temperature, F.data == "ai_temperature")
        self.dp.callback_query.register(self.cb_ai_max_tokens, F.data == "ai_max_tokens")
        self.dp.callback_query.register(self.cb_ai_test, F.data == "ai_test")

        # Обработчики состояний
        self.dp.message.register(
            self.handle_channel_input,
            StateFilter(AdminStates.waiting_for_channel)
        )
        self.dp.message.register(
            self.handle_blacklist_input,
            StateFilter(AdminStates.waiting_for_blacklist_word)
        )
        self.dp.message.register(
            self.handle_signature_input,
            StateFilter(AdminStates.waiting_for_signature)
        )
        self.dp.message.register(
            self.handle_post_edit,
            StateFilter(AdminStates.waiting_for_post_edit)
        )
        self.dp.message.register(
            self.handle_target_channel_input,
            StateFilter(AdminStates.waiting_for_target_channel)
        )

    async def start(self):
        """Запуск бота"""
        try:
            logger.info("Запуск Content Monster Bot...")

            # Запускаем polling
            self.is_running = True
            await self.dp.start_polling(self.bot)

        except Exception as e:
            logger.error(f"Ошибка запуска бота: {e}")
            raise

    async def stop(self):
        """Остановка бота"""
        try:
            logger.info("Остановка Content Monster Bot...")
            self.is_running = False

            await self.bot.session.close()

        except Exception as e:
            logger.error(f"Ошибка остановки бота: {e}")

    def _check_admin(self, user_id: int) -> bool:
        """Проверить, является ли пользователь админом"""
        return user_id == settings.admin_id

    async def cmd_start(self, message: Message):
        """Обработчик команды /start"""
        if not self._check_admin(message.from_user.id):
            await message.answer("❌ У вас нет доступа к этому боту")
            return

        await message.answer(
            "🤖 <b>Content Monster GPT Bot</b>\n\n"
            "Добро пожаловать в систему автоматического граббинга и публикации контента!\n\n"
            "Используйте /menu для открытия главного меню.",
            parse_mode="HTML"
        )

    async def cmd_menu(self, message: Message):
        """Обработчик команды /menu"""
        if not self._check_admin(message.from_user.id):
            return

        keyboard = self._get_main_menu_keyboard()
        await message.answer(
            "🎛 <b>Главное меню</b>\n\n"
            "Выберите действие:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    async def cmd_status(self, message: Message):
        """Обработчик команды /status"""
        if not self._check_admin(message.from_user.id):
            return

        async with self.db_session_factory() as db:
            # Получаем статистику
            donor_channels = await DonorChannelCRUD.get_all_active(db)
            target_channels = await TargetChannelCRUD.get_all_active(db)
            pending_posts = await PostCRUD.get_pending_posts(db)
            blacklist_words = await BlacklistCRUD.get_all_active(db)

        status_text = (
            "📊 <b>Статус системы</b>\n\n"
            f"📥 Каналы-доноры: {len(donor_channels)}/{settings.max_donor_channels}\n"
            f"📤 Целевые каналы: {len(target_channels)}\n"
            f"⏳ Постов на модерации: {len(pending_posts)}\n"
            f"🚫 Слов в черном списке: {len(blacklist_words)}\n\n"
            f"🧠 ИИ модель: {settings.openrouter_model}\n"
            f"📝 Подпись: {'Установлена' if settings.default_signature else 'Не установлена'}"
        )

        await message.answer(status_text, parse_mode="HTML")

    async def cmd_logs(self, message: Message):
        """Обработчик команды /logs"""
        if not self._check_admin(message.from_user.id):
            return

        # Здесь можно добавить отправку последних логов
        await message.answer("📋 Логи будут добавлены в следующей версии")

    def _get_main_menu_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру главного меню"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📥 Каналы-доноры", callback_data="donor_channels"),
                InlineKeyboardButton(text="📤 Целевые каналы", callback_data="target_channels")
            ],
            [
                InlineKeyboardButton(text="✅ Модерация", callback_data="moderation"),
                InlineKeyboardButton(text="⚙️ Настройки", callback_data="settings")
            ],
            [
                InlineKeyboardButton(text="🚫 Черный список", callback_data="blacklist"),
                InlineKeyboardButton(text="📊 Статистика", callback_data="statistics")
            ]
        ])
        return keyboard

    async def cb_main_menu(self, callback: CallbackQuery):
        """Коллбэк главного меню"""
        keyboard = self._get_main_menu_keyboard()
        await callback.message.edit_text(
            "🎛 <b>Главное меню</b>\n\nВыберите действие:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_donor_channels(self, callback: CallbackQuery):
        """Коллбэк управления каналами-донорами"""
        async with self.db_session_factory() as db:
            channels = await DonorChannelCRUD.get_all_active(db)

        text = f"📥 <b>Каналы-доноры ({len(channels)}/{settings.max_donor_channels})</b>\n\n"

        keyboard_buttons = []

        if channels:
            text += "Активные каналы:\n"
            for channel in channels:
                text += f"• {channel.title} (@{channel.username or 'без username'})\n"
                keyboard_buttons.append([
                    InlineKeyboardButton(
                        text=f"🗑 {channel.title[:20]}...",
                        callback_data=f"remove_donor:{channel.channel_id}"
                    )
                ])
        else:
            text += "Каналы не добавлены"

        # Кнопки управления
        if len(channels) < settings.max_donor_channels:
            keyboard_buttons.append([
                InlineKeyboardButton(text="➕ Добавить канал", callback_data="add_donor")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    async def cb_add_donor(self, callback: CallbackQuery, state: FSMContext):
        """Коллбэк добавления канала-донора"""
        await state.set_state(AdminStates.waiting_for_channel)
        await callback.message.edit_text(
            "📥 <b>Добавление канала-донора</b>\n\n"
            "Отправьте username канала (например: @channel_name) или ссылку на канал.\n\n"
            "❌ Отмена: /cancel",
            parse_mode="HTML"
        )
        await callback.answer()

    async def handle_channel_input(self, message: Message, state: FSMContext):
        """Обработчик ввода канала-донора"""
        if message.text == "/cancel":
            await state.clear()
            await message.answer("❌ Добавление канала отменено")
            return

        channel_input = message.text.strip()

        # Импортируем userbot здесь, чтобы избежать циклического импорта
        from core.userbot import userbot

        if not userbot:
            await message.answer("❌ Userbot не запущен")
            await state.clear()
            return

        success, result_message = await userbot.add_channel(channel_input)

        if success:
            await message.answer(f"✅ {result_message}")
        else:
            await message.answer(f"❌ {result_message}")

        await state.clear()

    async def cb_remove_donor(self, callback: CallbackQuery):
        """Коллбэк удаления канала-донора"""
        channel_id = int(callback.data.split(":")[1])

        from core.userbot import userbot

        if not userbot:
            await callback.answer("❌ Userbot не запущен", show_alert=True)
            return

        success, result_message = await userbot.remove_channel(channel_id)

        if success:
            await callback.answer(f"✅ {result_message}")
            # Обновляем список каналов
            await self.cb_donor_channels(callback)
        else:
            await callback.answer(f"❌ {result_message}", show_alert=True)

    async def cb_target_channels(self, callback: CallbackQuery):
        """Коллбэк управления целевыми каналами"""
        async with self.db_session_factory() as db:
            channels = await TargetChannelCRUD.get_all_active(db)

        text = f"📤 <b>Целевые каналы ({len(channels)})</b>\n\n"

        keyboard_buttons = []

        if channels:
            text += "Активные каналы:\n"
            for channel in channels:
                text += f"• {channel.title} (@{channel.username or 'без username'})\n"
        else:
            text += "Каналы не добавлены"

        keyboard_buttons.append([
            InlineKeyboardButton(text="➕ Добавить канал", callback_data="add_target")
        ])
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    async def cb_add_target(self, callback: CallbackQuery, state: FSMContext):
        """Коллбэк добавления целевого канала"""
        await state.set_state(AdminStates.waiting_for_target_channel)
        await callback.message.edit_text(
            "📤 <b>Добавление целевого канала</b>\n\n"
            "Отправьте username канала (например: @my_channel) где бот является администратором.\n\n"
            "❌ Отмена: /cancel",
            parse_mode="HTML"
        )
        await callback.answer()

    async def handle_target_channel_input(self, message: Message, state: FSMContext):
        """Обработчик ввода целевого канала"""
        if message.text == "/cancel":
            await state.clear()
            await message.answer("❌ Добавление канала отменено")
            return

        channel_input = message.text.strip()

        try:
            # Получаем информацию о канале
            chat = await self.bot.get_chat(channel_input)

            if chat.type != "channel":
                await message.answer("❌ Это не канал")
                await state.clear()
                return

            # Проверяем права бота
            bot_member = await self.bot.get_chat_member(chat.id, self.bot.id)
            if bot_member.status not in ["administrator", "creator"]:
                await message.answer("❌ Бот должен быть администратором канала")
                await state.clear()
                return

            # Добавляем в БД
            async with self.db_session_factory() as db:
                await TargetChannelCRUD.create(
                    db=db,
                    channel_id=chat.id,
                    title=chat.title,
                    username=chat.username
                )

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Добавлен целевой канал: {chat.title}",
                    module="telegram_bot",
                    user_id=message.from_user.id
                )

            await message.answer(f"✅ Канал '{chat.title}' успешно добавлен")

        except Exception as e:
            logger.error(f"Ошибка добавления целевого канала: {e}")
            await message.answer(f"❌ Ошибка: {e}")

        await state.clear()

    async def cb_moderation(self, callback: CallbackQuery):
        """Коллбэк модерации постов"""
        async with self.db_session_factory() as db:
            pending_posts = await PostCRUD.get_pending_posts(db)

        if not pending_posts:
            await callback.message.edit_text(
                "✅ <b>Модерация</b>\n\nНет постов для модерации",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                    InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
                ]]),
                parse_mode="HTML"
            )
            await callback.answer()
            return

        # Показываем первый пост
        await self._show_post_for_moderation(callback.message, pending_posts[0])
        await callback.answer()

    async def _show_post_for_moderation(self, message: Message, post):
        """Показать пост для модерации"""
        text = (
            f"📝 <b>Пост #{post.id} для модерации</b>\n\n"
            f"📥 Источник: {post.donor_channel.title}\n"
            f"🕐 Получен: {post.created_at.strftime('%d.%m.%Y %H:%M')}\n\n"
        )

        if post.original_text:
            text += f"<b>Оригинальный текст:</b>\n{post.original_text[:500]}...\n\n"

        if post.rewritten_text:
            text += f"<b>Переписанный текст:</b>\n{post.rewritten_text[:500]}...\n\n"

        if post.final_text:
            text += f"<b>Финальный текст:</b>\n{post.final_text[:500]}..."

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="✅ Одобрить", callback_data=f"approve:{post.id}"),
                InlineKeyboardButton(text="✏️ Редактировать", callback_data=f"edit:{post.id}")
            ],
            [
                InlineKeyboardButton(text="❌ Отклонить", callback_data=f"reject:{post.id}"),
                InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
            ]
        ])

        # Если есть медиа, отправляем с медиа
        if post.original_media and len(post.original_media) > 0:
            media_group = await self._prepare_media_group(post.original_media)
            if media_group:
                await message.answer_media_group(media_group)

        await message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

    async def _prepare_media_group(self, media_data: List[Dict]) -> List:
        """Подготовить медиа-группу для отправки"""
        media_group = []

        for media_item in media_data[:10]:  # Максимум 10 медиа в группе
            media_type = media_item.get("type")
            file_id = media_item.get("file_id")

            if not file_id:
                continue

            if media_type == "photo":
                media_group.append(InputMediaPhoto(media=file_id))
            elif media_type == "video":
                media_group.append(InputMediaVideo(media=file_id))
            elif media_type == "audio":
                media_group.append(InputMediaAudio(media=file_id))
            elif media_type == "document":
                media_group.append(InputMediaDocument(media=file_id))
            elif media_type == "animation":
                media_group.append(InputMediaAnimation(media=file_id))

        return media_group

    async def cb_approve_post(self, callback: CallbackQuery):
        """Коллбэк одобрения поста"""
        post_id = int(callback.data.split(":")[1])

        # Показываем выбор целевого канала
        async with self.db_session_factory() as db:
            target_channels = await TargetChannelCRUD.get_all_active(db)

        if not target_channels:
            await callback.answer("❌ Нет целевых каналов для публикации", show_alert=True)
            return

        keyboard_buttons = []
        for channel in target_channels:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=f"📤 {channel.title}",
                    callback_data=f"publish:{post_id}:{channel.id}"
                )
            ])

        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="moderation")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        await callback.message.edit_text(
            "📤 <b>Выберите канал для публикации:</b>",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_reject_post(self, callback: CallbackQuery):
        """Коллбэк отклонения поста"""
        post_id = int(callback.data.split(":")[1])

        async with self.db_session_factory() as db:
            await PostCRUD.update_status(db, post_id, "rejected")

            await LogCRUD.add_log(
                db=db,
                level="INFO",
                message=f"Пост #{post_id} отклонен",
                module="telegram_bot",
                user_id=callback.from_user.id,
                post_id=post_id
            )

        await callback.answer("❌ Пост отклонен")
        await self.cb_moderation(callback)

    async def cb_publish_post(self, callback: CallbackQuery):
        """Коллбэк публикации поста"""
        data_parts = callback.data.split(":")
        post_id = int(data_parts[1])
        target_channel_id = int(data_parts[2])

        success, message = await self.publish_post(post_id, target_channel_id)

        if success:
            await callback.answer(f"✅ {message}")
            await self.cb_moderation(callback)
        else:
            await callback.answer(f"❌ {message}", show_alert=True)

    async def cb_edit_post(self, callback: CallbackQuery, state: FSMContext):
        """Коллбэк редактирования поста"""
        post_id = int(callback.data.split(":")[1])

        await state.set_state(AdminStates.waiting_for_post_edit)
        await state.update_data(post_id=post_id)

        await callback.message.edit_text(
            "✏️ <b>Редактирование поста</b>\n\n"
            "Отправьте новый текст для поста.\n\n"
            "❌ Отмена: /cancel",
            parse_mode="HTML"
        )
        await callback.answer()

    async def handle_post_edit(self, message: Message, state: FSMContext):
        """Обработчик редактирования поста"""
        if message.text == "/cancel":
            await state.clear()
            await message.answer("❌ Редактирование отменено")
            return

        data = await state.get_data()
        post_id = data.get("post_id")

        new_text = message.text.strip()
        final_text = new_text + settings.default_signature

        async with self.db_session_factory() as db:
            await PostCRUD.update_status(
                db=db,
                post_id=post_id,
                status="pending",
                rewritten_text=new_text,
                final_text=final_text
            )

            await LogCRUD.add_log(
                db=db,
                level="INFO",
                message=f"Пост #{post_id} отредактирован",
                module="telegram_bot",
                user_id=message.from_user.id,
                post_id=post_id
            )

        await message.answer("✅ Пост обновлен")
        await state.clear()

    async def cb_settings(self, callback: CallbackQuery):
        """Коллбэк настроек"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📝 Изменить подпись", callback_data="change_signature"),
                InlineKeyboardButton(text="🧠 Настройки ИИ", callback_data="ai_settings")
            ],
            [
                InlineKeyboardButton(text="⏰ Расписание", callback_data="schedule_settings"),
                InlineKeyboardButton(text="🔄 Перезагрузка", callback_data="reload_system")
            ],
            [
                InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
            ]
        ])

        await callback.message.edit_text(
            "⚙️ <b>Настройки</b>\n\nВыберите что настроить:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_change_signature(self, callback: CallbackQuery, state: FSMContext):
        """Коллбэк изменения подписи"""
        await state.set_state(AdminStates.waiting_for_signature)

        current_signature = settings.default_signature

        await callback.message.edit_text(
            f"📝 <b>Изменение подписи</b>\n\n"
            f"Текущая подпись:\n<code>{current_signature}</code>\n\n"
            f"Отправьте новую подпись или /cancel для отмены:",
            parse_mode="HTML"
        )
        await callback.answer()

    async def handle_signature_input(self, message: Message, state: FSMContext):
        """Обработчик ввода новой подписи"""
        if message.text == "/cancel":
            await state.clear()
            await message.answer("❌ Изменение подписи отменено")
            return

        new_signature = message.text.strip()

        async with self.db_session_factory() as db:
            await SettingsCRUD.set_setting(
                db=db,
                key="default_signature",
                value=new_signature,
                description="Подпись для постов"
            )

        # Обновляем глобальную настройку
        settings.default_signature = new_signature

        await message.answer(f"✅ Подпись обновлена:\n<code>{new_signature}</code>", parse_mode="HTML")
        await state.clear()

    async def cb_blacklist(self, callback: CallbackQuery):
        """Коллбэк управления черным списком"""
        async with self.db_session_factory() as db:
            blacklist_words = await BlacklistCRUD.get_all_active(db)

        text = f"🚫 <b>Черный список ({len(blacklist_words)} слов)</b>\n\n"

        if blacklist_words:
            text += "Запрещенные слова:\n"
            for word in blacklist_words[:20]:  # Показываем первые 20
                text += f"• {word}\n"

            if len(blacklist_words) > 20:
                text += f"... и еще {len(blacklist_words) - 20} слов"
        else:
            text += "Черный список пуст"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="➕ Добавить слово", callback_data="add_blacklist"),
                InlineKeyboardButton(text="🗑 Очистить список", callback_data="clear_blacklist")
            ],
            [
                InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    async def cb_add_blacklist(self, callback: CallbackQuery, state: FSMContext):
        """Коллбэк добавления слова в черный список"""
        await state.set_state(AdminStates.waiting_for_blacklist_word)

        await callback.message.edit_text(
            "🚫 <b>Добавление слова в черный список</b>\n\n"
            "Отправьте слово или фразу для добавления в черный список.\n\n"
            "❌ Отмена: /cancel",
            parse_mode="HTML"
        )
        await callback.answer()

    async def handle_blacklist_input(self, message: Message, state: FSMContext):
        """Обработчик ввода слова для черного списка"""
        if message.text == "/cancel":
            await state.clear()
            await message.answer("❌ Добавление слова отменено")
            return

        word = message.text.strip().lower()

        async with self.db_session_factory() as db:
            try:
                await BlacklistCRUD.add_word(db, word)

                # Обновляем фильтр
                blacklist_words = await BlacklistCRUD.get_all_active(db)
                content_filter.update_blacklist(blacklist_words)

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Добавлено слово в черный список: {word}",
                    module="telegram_bot",
                    user_id=message.from_user.id
                )

                await message.answer(f"✅ Слово '{word}' добавлено в черный список")

            except Exception as e:
                await message.answer(f"❌ Ошибка: {e}")

        await state.clear()

    async def cb_statistics(self, callback: CallbackQuery):
        """Коллбэк статистики"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        keyboard = await admin_handlers.get_statistics_keyboard()
        await callback.message.edit_text(
            "📊 <b>Статистика</b>\n\nВыберите тип статистики:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_stats_general(self, callback: CallbackQuery):
        """Коллбэк общей статистики"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        stats_text = await admin_handlers.get_general_statistics()
        keyboard = await admin_handlers.get_statistics_keyboard()

        await callback.message.edit_text(
            stats_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_stats_posts(self, callback: CallbackQuery):
        """Коллбэк статистики постов"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        stats_text = await admin_handlers.get_posts_statistics()
        keyboard = await admin_handlers.get_statistics_keyboard()

        await callback.message.edit_text(
            stats_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_stats_today(self, callback: CallbackQuery):
        """Коллбэк статистики за сегодня"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        stats_text = await admin_handlers.get_today_statistics()
        keyboard = await admin_handlers.get_statistics_keyboard()

        await callback.message.edit_text(
            stats_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_stats_week(self, callback: CallbackQuery):
        """Коллбэк статистики за неделю"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        stats_text = await admin_handlers.get_week_statistics()
        keyboard = await admin_handlers.get_statistics_keyboard()

        await callback.message.edit_text(
            stats_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_schedule_settings(self, callback: CallbackQuery):
        """Коллбэк настроек расписания"""
        from handlers.settings_handlers import settings_handlers

        if not settings_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        keyboard = await settings_handlers.get_schedule_keyboard()
        await callback.message.edit_text(
            "⏰ <b>Настройки расписания</b>\n\nВыберите параметр для настройки:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_ai_change_model(self, callback: CallbackQuery):
        """Коллбэк смены модели ИИ"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        models = await admin_handlers.get_available_models()

        keyboard_buttons = []
        for model in models[:10]:  # Показываем первые 10 моделей
            model_name = model.split("/")[-1]  # Берем только название модели
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=model_name,
                    callback_data=f"select_model:{model}"
                )
            ])

        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="ai_settings")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        await callback.message.edit_text(
            "🤖 <b>Выберите модель ИИ:</b>",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_ai_temperature(self, callback: CallbackQuery):
        """Коллбэк настройки температуры ИИ"""
        from handlers.settings_handlers import settings_handlers

        if not settings_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        keyboard = settings_handlers.get_temperature_keyboard()
        await callback.message.edit_text(
            "🌡️ <b>Настройка температуры ИИ</b>\n\n"
            "Температура влияет на креативность ответов:\n"
            "• Низкая (0.3) - более предсказуемые ответы\n"
            "• Высокая (1.2) - более креативные ответы",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_ai_max_tokens(self, callback: CallbackQuery):
        """Коллбэк настройки максимальных токенов"""
        from handlers.settings_handlers import settings_handlers

        if not settings_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        keyboard = settings_handlers.get_max_tokens_keyboard()
        await callback.message.edit_text(
            "📏 <b>Максимальное количество токенов</b>\n\n"
            "Влияет на длину генерируемого текста:\n"
            "• 500 токенов ≈ 375 слов\n"
            "• 1000 токенов ≈ 750 слов\n"
            "• 2000 токенов ≈ 1500 слов",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        await callback.answer()

    async def cb_ai_test(self, callback: CallbackQuery):
        """Коллбэк тестирования ИИ API"""
        from handlers.admin_handlers import admin_handlers

        if not admin_handlers:
            await callback.answer("❌ Обработчики не инициализированы", show_alert=True)
            return

        await callback.answer("🧪 Тестирование API...")

        test_result = await admin_handlers.test_ai_api()

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔙 Назад", callback_data="ai_settings")]
        ])

        await callback.message.edit_text(
            test_result,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    async def publish_post(self, post_id: int, target_channel_id: int) -> tuple[bool, str]:
        """Опубликовать пост в целевом канале"""
        try:
            async with self.db_session_factory() as db:
                # Получаем пост
                post = await PostCRUD.get_by_id(db, post_id)
                if not post:
                    return False, "Пост не найден"

                # Получаем целевой канал
                target_channel = await TargetChannelCRUD.get_by_id(db, target_channel_id)
                if not target_channel:
                    return False, "Целевой канал не найден"

                # Публикуем пост
                if post.original_media and len(post.original_media) > 0:
                    # Пост с медиа
                    media_group = await self._prepare_media_group(post.original_media)
                    if media_group:
                        # Добавляем текст к первому медиа
                        if post.final_text:
                            media_group[0].caption = post.final_text
                            media_group[0].parse_mode = "HTML"

                        await self.bot.send_media_group(
                            chat_id=target_channel.channel_id,
                            media=media_group
                        )
                    else:
                        # Если медиа не удалось подготовить, отправляем только текст
                        if post.final_text:
                            await self.bot.send_message(
                                chat_id=target_channel.channel_id,
                                text=post.final_text,
                                parse_mode="HTML"
                            )
                else:
                    # Только текст
                    if post.final_text:
                        await self.bot.send_message(
                            chat_id=target_channel.channel_id,
                            text=post.final_text,
                            parse_mode="HTML"
                        )

                # Обновляем статус поста
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="published",
                    target_channel_id=target_channel_id
                )

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Пост #{post_id} опубликован в {target_channel.title}",
                    module="telegram_bot",
                    post_id=post_id
                )

                return True, f"Пост опубликован в '{target_channel.title}'"

        except Exception as e:
            logger.error(f"Ошибка публикации поста {post_id}: {e}")
            return False, f"Ошибка публикации: {e}"


# Глобальный экземпляр бота (будет инициализирован в main.py)
telegram_bot: Optional[ContentMonsterBot] = None
