# 🤖 Content Monster GPT Bot

Продвинутый Telegram-граббер-контент-бот для автоматического копирования, переписывания и публикации постов из каналов-доноров в ваши каналы.

## ✨ Возможности

### 🔄 Автоматизация
- **Мониторинг каналов-доноров** в реальном времени через userbot
- **ИИ-переписывание** контента через OpenRouter API
- **Автоматическая публикация** по расписанию
- **Фильтрация контента** по черному списку

### 🎛️ Управление
- **Удобный интерфейс** через Telegram бота
- **Модерация постов** с предварительным просмотром
- **Гибкие настройки** ИИ и расписания
- **Подробное логирование** всех операций

### 📱 Функции
- Поддержка всех типов медиа (фото, видео, аудио, документы)
- Сохранение оригинального порядка медиа-альбомов
- Настраиваемые подписи к постам
- Черный список запрещенных слов
- Статистика и мониторинг

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Настройка конфигурации

Скопируйте файл с примером настроек:
```bash
copy .env.example .env
```

Отредактируйте `.env` файл:

```env
# Telegram Bot API
BOT_TOKEN=your_bot_token_here

# Telegram Userbot (Pyrogram)
API_ID=your_api_id_here
API_HASH=your_api_hash_here

# OpenRouter API
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Admin settings
ADMIN_ID=your_telegram_user_id_here
```

### 3. Получение необходимых токенов

#### Telegram Bot Token
1. Напишите [@BotFather](https://t.me/BotFather)
2. Создайте нового бота командой `/newbot`
3. Скопируйте полученный токен

#### API_ID и API_HASH
1. Перейдите на [my.telegram.org](https://my.telegram.org)
2. Войдите в аккаунт
3. Создайте новое приложение
4. Скопируйте `api_id` и `api_hash`

#### OpenRouter API Key
1. Зарегистрируйтесь на [OpenRouter.ai](https://openrouter.ai)
2. Пополните баланс
3. Создайте API ключ в настройках

#### Admin ID
1. Напишите [@userinfobot](https://t.me/userinfobot)
2. Скопируйте ваш User ID

### 4. Запуск

```bash
python main.py
```

## 📖 Подробная настройка

### Настройка каналов-доноров

1. Откройте бота и нажмите `/menu`
2. Выберите "📥 Каналы-доноры"
3. Нажмите "➕ Добавить канал"
4. Отправьте username канала (например: `@channel_name`)

**Важно:** Ваш аккаунт должен быть подписан на канал-донор!

### Настройка целевых каналов

1. В меню выберите "📤 Целевые каналы"
2. Нажмите "➕ Добавить канал"
3. Отправьте username вашего канала

**Важно:** Бот должен быть администратором целевого канала!

### Настройка ИИ

Доступные модели через OpenRouter:
- `openai/gpt-4-turbo-preview` (рекомендуется)
- `openai/gpt-3.5-turbo`
- `anthropic/claude-3-sonnet`
- `mistralai/mistral-7b-instruct`

Измените модель в файле `.env`:
```env
OPENROUTER_MODEL=openai/gpt-4-turbo-preview
```

### Черный список

Добавьте запрещенные слова через меню "🚫 Черный список":
- Реклама
- Спам
- Нежелательные термины

## 🎯 Использование

### Процесс работы

1. **Мониторинг** - userbot отслеживает новые посты в каналах-донорах
2. **Фильтрация** - проверка на черный список и качество контента
3. **ИИ-обработка** - переписывание текста через OpenRouter
4. **Модерация** - вы получаете пост для одобрения/редактирования
5. **Публикация** - пост публикуется в выбранном целевом канале

### Команды бота

- `/start` - Приветствие
- `/menu` - Главное меню
- `/status` - Статус системы
- `/logs` - Просмотр логов

### Модерация постов

Когда пост готов к модерации, вы получите сообщение с:
- Оригинальным текстом
- Переписанным текстом
- Медиа (если есть)
- Кнопками управления:
  - ✅ **Одобрить** - выбрать канал для публикации
  - ✏️ **Редактировать** - изменить текст
  - ❌ **Отклонить** - отклонить пост

## ⚙️ Дополнительные настройки

### Расписание публикации

В файле `.env` можно настроить:
```env
DEFAULT_POST_INTERVAL=7200  # Интервал между постами (секунды)
MAX_POSTS_PER_HOUR=10      # Максимум постов в час
```

### Настройки ИИ

```env
AI_TEMPERATURE=0.7         # Креативность (0.0-1.0)
AI_MAX_TOKENS=2000        # Максимум токенов в ответе
```

### Медиа настройки

```env
MAX_FILE_SIZE=50          # Максимальный размер файла (MB)
SUPPORTED_MEDIA_TYPES=photo,video,audio,document,animation
```

## 🔧 Устранение неполадок

### Частые проблемы

**Userbot не может читать канал**
- Убедитесь, что подписаны на канал
- Проверьте правильность API_ID и API_HASH
- Канал может быть приватным

**Бот не может публиковать**
- Добавьте бота в администраторы канала
- Дайте права на публикацию сообщений

**ИИ не работает**
- Проверьте API ключ OpenRouter
- Убедитесь в наличии средств на балансе
- Проверьте доступность модели

**База данных**
- При первом запуске создается автоматически
- Файл: `content_monster.db`

### Логи

Логи сохраняются в папке `logs/`:
- `content_monster.log` - основные логи
- `errors.log` - только ошибки

## 📊 Мониторинг

### Статистика

Команда `/status` показывает:
- Количество каналов-доноров
- Количество целевых каналов  
- Посты на модерации
- Слова в черном списке
- Текущую ИИ модель

### Производительность

Система может обрабатывать:
- До 5 каналов-доноров одновременно
- Неограниченное количество целевых каналов
- Автоматическую публикацию по расписанию

## 🛡️ Безопасность

- Все настройки доступны только админу (по ADMIN_ID)
- API ключи хранятся в переменных окружения
- Логирование всех действий
- Автоматическая очистка старых логов

## 📝 Лицензия

Этот проект создан для образовательных целей. Используйте ответственно и соблюдайте правила Telegram.

## 🆘 Поддержка

При возникновении проблем:
1. Проверьте логи в папке `logs/`
2. Убедитесь в правильности настроек `.env`
3. Проверьте права бота и доступы userbot

---

**Content Monster GPT Bot** - мощный инструмент для автоматизации контент-маркетинга в Telegram! 🚀
