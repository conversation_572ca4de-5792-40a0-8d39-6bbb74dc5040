"""
Фильтры контента для Content Monster GPT Bot
"""
import re
from typing import List, Optional
from loguru import logger


class ContentFilter:
    """Класс для фильтрации контента"""
    
    def __init__(self, blacklist_words: List[str] = None):
        self.blacklist_words = [word.lower() for word in (blacklist_words or [])]
        self.update_patterns()
    
    def update_blacklist(self, blacklist_words: List[str]):
        """Обновить черный список слов"""
        self.blacklist_words = [word.lower() for word in blacklist_words]
        self.update_patterns()
        logger.info(f"Черный список обновлен: {len(self.blacklist_words)} слов")
    
    def update_patterns(self):
        """Обновить регулярные выражения для поиска"""
        if not self.blacklist_words:
            self.pattern = None
            return
        
        # Создаем паттерн для поиска слов с учетом границ слов
        escaped_words = [re.escape(word) for word in self.blacklist_words]
        pattern_str = r'\b(?:' + '|'.join(escaped_words) + r')\b'
        self.pattern = re.compile(pattern_str, re.IGNORECASE | re.UNICODE)
    
    def contains_blacklisted_words(self, text: str) -> tuple[bool, List[str]]:
        """
        Проверить, содержит ли текст слова из черного списка
        
        Args:
            text: Текст для проверки
            
        Returns:
            tuple: (содержит_запрещенные_слова, список_найденных_слов)
        """
        if not text or not self.pattern:
            return False, []
        
        # Ищем совпадения
        matches = self.pattern.findall(text.lower())
        found_words = list(set(matches))  # Убираем дубликаты
        
        if found_words:
            logger.warning(f"Найдены запрещенные слова: {found_words}")
            return True, found_words
        
        return False, []
    
    def is_post_allowed(self, text: str = None, media_count: int = 0) -> tuple[bool, str]:
        """
        Проверить, разрешен ли пост для обработки
        
        Args:
            text: Текст поста
            media_count: Количество медиа файлов
            
        Returns:
            tuple: (разрешен, причина_отклонения)
        """
        # Проверка на пустой контент
        if not text and media_count == 0:
            return False, "Пост не содержит текста и медиа"
        
        # Проверка минимальной длины текста
        if text and len(text.strip()) < 10:
            return False, "Текст слишком короткий (менее 10 символов)"
        
        # Проверка на спам (много повторяющихся символов)
        if text and self._is_spam_text(text):
            return False, "Текст похож на спам"
        
        # Проверка черного списка
        if text:
            contains_blacklisted, found_words = self.contains_blacklisted_words(text)
            if contains_blacklisted:
                return False, f"Содержит запрещенные слова: {', '.join(found_words)}"
        
        return True, ""
    
    def _is_spam_text(self, text: str) -> bool:
        """Проверить, является ли текст спамом"""
        if not text:
            return False
        
        # Проверка на много повторяющихся символов
        char_counts = {}
        for char in text:
            if char.isalnum():
                char_counts[char] = char_counts.get(char, 0) + 1
        
        total_chars = sum(char_counts.values())
        if total_chars == 0:
            return False
        
        # Если какой-то символ составляет более 30% текста
        for count in char_counts.values():
            if count / total_chars > 0.3:
                return True
        
        # Проверка на много повторяющихся слов
        words = text.lower().split()
        if len(words) > 5:
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1
            
            # Если какое-то слово повторяется более 50% от общего количества
            for count in word_counts.values():
                if count / len(words) > 0.5:
                    return True
        
        return False
    
    def clean_text(self, text: str) -> str:
        """Очистить текст от нежелательных элементов"""
        if not text:
            return text
        
        # Удаляем лишние пробелы и переносы строк
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Удаляем повторяющиеся знаки препинания
        cleaned = re.sub(r'[!]{3,}', '!!!', cleaned)
        cleaned = re.sub(r'[?]{3,}', '???', cleaned)
        cleaned = re.sub(r'[.]{4,}', '...', cleaned)
        
        # Удаляем избыточные эмодзи (более 5 подряд одинаковых)
        cleaned = re.sub(r'([\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF])\1{4,}', r'\1\1\1', cleaned)
        
        return cleaned
    
    def extract_hashtags(self, text: str) -> List[str]:
        """Извлечь хештеги из текста"""
        if not text:
            return []
        
        hashtag_pattern = r'#[\w\u0400-\u04FF]+'
        hashtags = re.findall(hashtag_pattern, text)
        return hashtags
    
    def extract_mentions(self, text: str) -> List[str]:
        """Извлечь упоминания из текста"""
        if not text:
            return []
        
        mention_pattern = r'@[\w\u0400-\u04FF]+'
        mentions = re.findall(mention_pattern, text)
        return mentions
    
    def extract_urls(self, text: str) -> List[str]:
        """Извлечь URL из текста"""
        if not text:
            return []
        
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, text)
        return urls
    
    def remove_urls(self, text: str) -> str:
        """Удалить URL из текста"""
        if not text:
            return text
        
        url_pattern = r'https?://[^\s]+'
        return re.sub(url_pattern, '', text).strip()
    
    def get_text_stats(self, text: str) -> dict:
        """Получить статистику текста"""
        if not text:
            return {
                "length": 0,
                "words": 0,
                "sentences": 0,
                "hashtags": 0,
                "mentions": 0,
                "urls": 0
            }
        
        words = len(text.split())
        sentences = len(re.findall(r'[.!?]+', text))
        hashtags = len(self.extract_hashtags(text))
        mentions = len(self.extract_mentions(text))
        urls = len(self.extract_urls(text))
        
        return {
            "length": len(text),
            "words": words,
            "sentences": sentences,
            "hashtags": hashtags,
            "mentions": mentions,
            "urls": urls
        }


# Глобальный экземпляр фильтра
content_filter = ContentFilter()
