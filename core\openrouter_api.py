"""
OpenRouter API интеграция для Content Monster GPT Bot
"""
import asyncio
import time
from typing import Optional, Dict, Any
import aiohttp
from loguru import logger

from config.settings import settings, get_rewrite_prompt


class OpenRouterAPI:
    """Класс для работы с OpenRouter API"""
    
    def __init__(self):
        self.api_key = settings.openrouter_api_key
        self.base_url = settings.openrouter_base_url
        self.model = settings.openrouter_model
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, messages: list, **kwargs) -> Dict[str, Any]:
        """Выполнить запрос к OpenRouter API"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/content-monster-gpt",
            "X-Title": "Content Monster GPT Bot"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get("temperature", settings.ai_temperature),
            "max_tokens": kwargs.get("max_tokens", settings.ai_max_tokens),
            "stream": False
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"OpenRouter API error {response.status}: {error_text}")
                    raise Exception(f"API request failed: {response.status}")
                    
        except asyncio.TimeoutError:
            logger.error("OpenRouter API request timeout")
            raise Exception("API request timeout")
        except Exception as e:
            logger.error(f"OpenRouter API request error: {e}")
            raise
    
    async def rewrite_text(
        self, 
        text: str, 
        prompt_type: str = "default",
        custom_prompt: str = None,
        **kwargs
    ) -> tuple[str, float]:
        """
        Переписать текст с помощью ИИ
        
        Args:
            text: Исходный текст
            prompt_type: Тип промпта (default, news, entertainment)
            custom_prompt: Кастомный промпт
            **kwargs: Дополнительные параметры для API
        
        Returns:
            tuple: (переписанный_текст, время_обработки)
        """
        start_time = time.time()
        
        try:
            # Выбираем промпт
            if custom_prompt:
                prompt = custom_prompt.format(text=text)
            else:
                prompt = get_rewrite_prompt(prompt_type).format(text=text)
            
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            logger.info(f"Отправляем запрос к OpenRouter API с моделью {self.model}")
            
            response = await self._make_request(messages, **kwargs)
            
            # Извлекаем текст ответа
            if "choices" in response and len(response["choices"]) > 0:
                rewritten_text = response["choices"][0]["message"]["content"].strip()
                processing_time = time.time() - start_time
                
                logger.info(f"Текст успешно переписан за {processing_time:.2f} сек")
                return rewritten_text, processing_time
            else:
                raise Exception("Некорректный ответ от API")
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Ошибка при переписывании текста: {e}")
            raise Exception(f"Не удалось переписать текст: {e}")
    
    async def check_api_status(self) -> bool:
        """Проверить доступность API"""
        try:
            test_messages = [
                {
                    "role": "user", 
                    "content": "Привет! Это тестовое сообщение."
                }
            ]
            
            response = await self._make_request(
                test_messages, 
                max_tokens=50, 
                temperature=0.1
            )
            
            return "choices" in response and len(response["choices"]) > 0
            
        except Exception as e:
            logger.error(f"API недоступен: {e}")
            return False
    
    async def get_available_models(self) -> list:
        """Получить список доступных моделей"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.get(
                f"{self.base_url}/models",
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    return [model["id"] for model in data.get("data", [])]
                else:
                    logger.error(f"Ошибка получения моделей: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Ошибка при получении списка моделей: {e}")
            return []
    
    def set_model(self, model: str):
        """Установить модель для использования"""
        self.model = model
        logger.info(f"Модель изменена на: {model}")
    
    async def close(self):
        """Закрыть сессию"""
        if self.session:
            await self.session.close()


# Глобальный экземпляр API
openrouter_api = OpenRouterAPI()
