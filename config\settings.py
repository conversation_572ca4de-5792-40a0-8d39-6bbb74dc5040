"""
Конфигурация Content Monster GPT Bot
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Настройки приложения"""

    # Telegram Bot API
    bot_token: str = Field(..., env="BOT_TOKEN")

    # Telegram Userbot (Pyrogram)
    api_id: int = Field(..., env="API_ID")
    api_hash: str = Field(..., env="API_HASH")
    session_name: str = Field("content_monster_session", env="SESSION_NAME")

    # OpenRouter API
    openrouter_api_key: str = Field(..., env="OPENROUTER_API_KEY")
    openrouter_model: str = Field("openai/gpt-4-turbo-preview", env="OPENROUTER_MODEL")
    openrouter_base_url: str = Field("https://openrouter.ai/api/v1", env="OPENROUTER_BASE_URL")

    # Admin settings
    admin_id: int = Field(..., env="ADMIN_ID")

    # Database
    database_url: str = Field("sqlite+aiosqlite:///./content_monster.db", env="DATABASE_URL")

    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/content_monster.log", env="LOG_FILE")

    # Default settings
    default_signature: str = Field("\n\n🤖 Powered by Content Monster GPT", env="DEFAULT_SIGNATURE")
    default_post_interval: int = Field(7200, env="DEFAULT_POST_INTERVAL")  # 2 hours
    max_donor_channels: int = Field(5, env="MAX_DONOR_CHANNELS")

    # Media settings
    max_file_size: int = Field(50, env="MAX_FILE_SIZE")  # MB
    supported_media_types: str = Field(
        "photo,video,audio,document,animation",
        env="SUPPORTED_MEDIA_TYPES"
    )

    # Ratelimiting
    max_posts_per_hour: int = Field(10, env="MAX_POSTS_PER_HOUR")

    # OpenRouter settings
    ai_temperature: float = Field(0.7, env="AI_TEMPERATURE")
    ai_max_tokens: int = Field(2000, env="AI_MAX_TOKENS")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def get_supported_media_types(self) -> List[str]:
        """Получить список поддерживаемых типов медиа"""
        return [t.strip() for t in self.supported_media_types.split(",")]


# Глобальный экземпляр настроек
settings = Settings()


# Системные промпты для ИИ
REWRITE_PROMPTS = {
    "default": """
Ты - профессиональный копирайтер. Перепиши данный текст, сохранив основную идею и смысл, но изменив стиль и формулировки.

Требования:
- Сохрани ключевую информацию
- Измени структуру предложений
- Используй синонимы
- Сделай текст более живым и интересным
- Сохрани эмодзи, если они есть
- НЕ добавляй никаких подписей или дополнительной информации

Текст для переписывания:
{text}

Переписанный текст:""",

    "news": """
Ты - редактор новостного канала. Перепиши новость, сохранив факты, но изменив подачу.

Требования:
- Сохрани все факты и даты
- Измени заголовок и структуру
- Используй журналистский стиль
- Сделай текст более цепляющим
- Сохрани важные детали

Новость для переписывания:
{text}

Переписанная новость:""",

    "entertainment": """
Ты - создатель развлекательного контента. Перепиши текст, сделав его более увлекательным.

Требования:
- Сохрани основную идею
- Добавь больше эмоций
- Используй более живой язык
- Сделай текст более захватывающим
- Сохрани эмодзи и форматирование

Текст для переписывания:
{text}

Переписанный текст:"""
}


def get_rewrite_prompt(prompt_type: str = "default") -> str:
    """Получить промпт для переписывания"""
    return REWRITE_PROMPTS.get(prompt_type, REWRITE_PROMPTS["default"])
