#!/usr/bin/env python3
"""
Тест Google Gemma API через OpenRouter
"""
import asyncio
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
load_dotenv()

from core.openrouter_api import openrouter_api


async def test_gemma_api():
    """Тестирование Google Gemma API"""
    
    print("🧪 Тестирование Google Gemma API через OpenRouter...")
    print("=" * 60)
    
    test_texts = [
        "Привет! Это тестовое сообщение для проверки работы Google Gemma модели.",
        "Сегодня отличная погода для прогулки в парке. Солнце светит ярко, птицы поют.",
        "Новая технология искусственного интеллекта позволяет автоматизировать многие процессы."
    ]
    
    try:
        async with openrouter_api:
            print(f"🤖 Используемая модель: {openrouter_api.model}")
            print(f"🌡️ Температура: {openrouter_api.ai_temperature}")
            print()
            
            # Проверяем доступность API
            print("🔍 Проверка доступности API...")
            is_available = await openrouter_api.check_api_status()
            
            if not is_available:
                print("❌ API недоступен!")
                return
            
            print("✅ API доступен!")
            print()
            
            # Тестируем переписывание текстов
            for i, text in enumerate(test_texts, 1):
                print(f"📝 Тест {i}/3:")
                print(f"Исходный текст: {text}")
                print("Обработка...", end="", flush=True)
                
                try:
                    rewritten_text, processing_time = await openrouter_api.rewrite_text(
                        text=text,
                        prompt_type="default"
                    )
                    
                    print(f" ✅ Готово за {processing_time:.2f} сек")
                    print(f"Результат: {rewritten_text}")
                    print("-" * 60)
                    
                except Exception as e:
                    print(f" ❌ Ошибка: {e}")
                    print("-" * 60)
            
            # Тестируем разные типы промптов
            print("\n🎭 Тестирование разных типов промптов:")
            
            test_text = "В городе открылся новый ресторан с итальянской кухней. Меню включает пиццу, пасту и ризотто."
            
            prompt_types = ["default", "news", "entertainment"]
            
            for prompt_type in prompt_types:
                print(f"\n📋 Промпт: {prompt_type}")
                print("Обработка...", end="", flush=True)
                
                try:
                    rewritten_text, processing_time = await openrouter_api.rewrite_text(
                        text=test_text,
                        prompt_type=prompt_type
                    )
                    
                    print(f" ✅ Готово за {processing_time:.2f} сек")
                    print(f"Результат: {rewritten_text}")
                    
                except Exception as e:
                    print(f" ❌ Ошибка: {e}")
            
            print("\n" + "=" * 60)
            print("🎉 Тестирование завершено!")
            
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")


if __name__ == "__main__":
    asyncio.run(test_gemma_api())
