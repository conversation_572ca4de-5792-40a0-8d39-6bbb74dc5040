.container{max-width: 960px;border-radius:8px;-moz-border-radius:8px;-webkit-border-radius:8px;}
#text_reply{width: 100%;height: 131px;padding: 2px 5px;color:#807474;background:#fffbc3;margin-bottom:27px;font-size: 13px;}
body{background:url('../img/bg-dark-4780.jpg');}
.container{background:#f1f1f1;margin-top: 20px;}
.col-md-12{padding:5px 0 5px;}
label{font-size:11px;}
.col-md-5{display:inline-table;}
.col-md-2{display:inline-table;padding:0;}
.col-md-3,.col-md-4{display: inline-table;padding:0;}
.alert-success{padding: 1px 5px;font-size:10px;margin-top:2px;}
.btn-success{width:100%;}
.custom-control{line-height:22px;}
.add_channel{margin-top:8px;width:100px;padding:3px;font-size:12px;}
.table-bordered thead th{font-size:12px;text-align:center;}
.del_channel{cursor:pointer;}
.del_channel:hover{color:red;}
.fa-pause{color:#dc3545;}
.btn-primary {color: #fff;background-color: #2687ef;border-color: #007bff;font-size: 11px;padding: 3px 9px;}
.change_pwd{margin-right:25px;}
.form-control:focus {border-color:#a7a7a7;box-shadow: none;}
#load{margin:0 10px;}
.table td{padding:2px 5px;}
.form-check-inline{margin-right:-3px;}
#spam_filter, #replace_words, #filter_links, #word_send_post{height:82px;font-size:11px;padding: 3px 5px;color: #bd2222;background: #f9f9f9;}

  @media screen and (min-width:150px) and (max-width:900px) 
  {
	  .invisible_{display:none;}
	  #channels > tr > td > a{font-size:12px;}
	  .container{padding:0 3px;}
	  .col-md-12{padding:0;}
  }
  
   .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{background-color:#e8e8e8;border-color:#dee2e6 #cecece #fff;}
  .select2-container .select2-selection--single .select2-selection__rendered{font-size:13px;}
  .select2-container{font-size:13px;color:#474c50;}
  .nav-tabs .nav-item {margin-bottom: -3px;}
  .nav-tabs .nav-link.active {background-color: #e4e4e4;border-color: #cccccc #d4d4d4 #dcdcdc;}
  .navbar-light .navbar-brand {color: #9e9e9e;}
  .my-lg-0{margin-right: 24px;}
  .bot_vote_info{background:#efefef8f;color:#4b4c4c;font-size:12px;border:1px #e8e8e8 solid;padding-left:25px;}
  .bot_vote_info > b{color:#306daf;}
  .alert-success {color:#4e504e;background-color:#efefef8f;border-color:#e6e6e6;margin-top:5px;}
  .bg-light > label{font-size:14px;color:#9a9a9a;padding:13px 21px 6px 0;}
  .form-control{background-color: #f9f9f9;}
  .nav-tabs{margin:0 -15px;}
  a{color:#6f6c6c;font-size:14px;}
  table > tbody > tr > td{color:#6b7177;font-size:14px; }
  table > tbody > tr > td > a{color:#585858;font-size:12px;}
  .center{text-align:center;}
  .btn_posted
  {
    color: #4a4747;
    background-color: #eaeaea;
    border-color: #c5c5c5;
    font-size: 11px;
    padding: 2px 10px;
  }
  .chk_inline > .form-check-inline{width:100%;}
  .chk_inline > label > b{color:#4c4a4b;}
  .chk_inline_14{display:block;}
  #time_box{padding-left:25px;}
  #limit_box{padding-left:25px;}
  #sugn_channel{font-size:12px;color:#777777;height:76px;}
  .table th {padding:7px 1px;}
  #setting_channel{padding:0;margin:0;min-height:682px;}
  
  .form-control:disabled, .form-control[readonly] {
    background-color: #e4e4e4!important;
	color: #9e9c9c!important;
  }
  
  .nav-link-mainmenu{display:none;}
  .nav-link 
  {
    display: block;
    padding: 5px 14px;
	font-size: 12px;
	color: #424443;
  }
  .select2-container--default .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
  }
  .select2-container--default .select2-selection--single .select2-selection__rendered {line-height: 26px;}
  .table-bordered td, .table-bordered th {border-bottom: 1px solid #d4d4d4;}
   #replace_username{height: 31px;font-size: 12px;}
   .del_account{padding: 3px 9px;font-size: 11px;}
	.del_account, .change_account{margin-top:6px;width:128px;}
	.change_name_channel{width: 100%;margin-top: 4px;}