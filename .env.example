# Telegram Bot API
BOT_TOKEN=your_bot_token_here

# Telegram Userbot (Pyrogram)
API_ID=your_api_id_here
API_HASH=your_api_hash_here
SESSION_NAME=content_monster_session

# OpenRouter API
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=openai/gpt-4-turbo-preview

# Admin settings
ADMIN_ID=your_telegram_user_id_here

# Database
DATABASE_URL=sqlite+aiosqlite:///./content_monster.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/content_monster.log

# Default settings
DEFAULT_SIGNATURE="\n\n🤖 Powered by Content Monster GPT"
DEFAULT_POST_INTERVAL=7200  # 2 hours in seconds
MAX_DONOR_CHANNELS=5

# Media settings
MAX_FILE_SIZE=50  # MB
SUPPORTED_MEDIA_TYPES=photo,video,audio,document,animation
