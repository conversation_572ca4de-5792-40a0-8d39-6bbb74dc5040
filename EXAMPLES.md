# 📚 Примеры использования Content Monster GPT Bot

## 🎯 Сценарии использования

### 1. Новостной канал

**Задача:** Автоматически собирать новости из 3-5 источников и публиковать в своем канале с переписыванием.

**Настройка:**
1. Добавьте каналы-доноры: `@news_channel1`, `@news_channel2`, `@breaking_news`
2. Добавьте свой канал: `@my_news_channel`
3. Установите тип промпта: "Новости"
4. Настройте расписание: каждые 2 часа
5. Добавьте в черный список: "реклама", "спонсор", "промо"

**Результат:** Автоматический новостной канал с уникальным контентом.

### 2. Развлекательный контент

**Задача:** Собирать мемы и развлекательный контент, адаптировать под свою аудиторию.

**Настройка:**
1. Каналы-доноры: `@memes`, `@funny_videos`, `@entertainment`
2. Целевой канал: `@my_fun_channel`
3. Тип промпта: "Развлечения"
4. Температура ИИ: 0.9 (более креативно)
5. Интервал: каждые 30 минут

**Результат:** Живой развлекательный канал с адаптированным контентом.

### 3. Образовательный контент

**Задача:** Собирать образовательные материалы и адаптировать их для своей аудитории.

**Настройка:**
1. Каналы-доноры: `@education`, `@science_facts`, `@tech_tips`
2. Целевой канал: `@my_education`
3. Тип промпта: "Обычный"
4. Температура: 0.5 (сбалансированно)
5. Ручная модерация всех постов

## 🔧 Примеры настроек

### Настройки для новостного канала

```env
# .env настройки
OPENROUTER_MODEL=openai/gpt-4-turbo-preview
AI_TEMPERATURE=0.5
AI_MAX_TOKENS=1500
DEFAULT_POST_INTERVAL=7200
MAX_POSTS_PER_HOUR=5
DEFAULT_SIGNATURE="\n\n📰 @my_news_channel"
```

**Промпт для новостей:**
```
Ты - редактор новостного канала. Перепиши новость, сохранив факты, но изменив подачу.

Требования:
- Сохрани все факты и даты
- Измени заголовок и структуру
- Используй журналистский стиль
- Сделай текст более цепляющим
- Сохрани важные детали
```

### Настройки для развлекательного канала

```env
OPENROUTER_MODEL=anthropic/claude-3-sonnet
AI_TEMPERATURE=0.9
AI_MAX_TOKENS=1000
DEFAULT_POST_INTERVAL=1800
MAX_POSTS_PER_HOUR=15
DEFAULT_SIGNATURE="\n\n😄 Больше веселья в @my_fun_channel"
```

### Настройки для бизнес-канала

```env
OPENROUTER_MODEL=openai/gpt-4
AI_TEMPERATURE=0.3
AI_MAX_TOKENS=2000
DEFAULT_POST_INTERVAL=14400
MAX_POSTS_PER_HOUR=3
DEFAULT_SIGNATURE="\n\n💼 Подписывайтесь: @business_insights"
```

## 📋 Примеры черного списка

### Для новостного канала
```
реклама
спонсор
промо
казино
ставки
форекс
криптовалюта
инвестиции
заработок
```

### Для семейного канала
```
18+
взрослый
эротика
насилие
алкоголь
наркотики
мат
ругательства
```

### Для образовательного канала
```
реклама
продажа
скидка
акция
промокод
заработок
быстрые деньги
```

## ⏰ Примеры расписания

### Активный канал (новости)
```json
{
  "monday": ["08:00", "12:00", "16:00", "20:00"],
  "tuesday": ["08:00", "12:00", "16:00", "20:00"],
  "wednesday": ["08:00", "12:00", "16:00", "20:00"],
  "thursday": ["08:00", "12:00", "16:00", "20:00"],
  "friday": ["08:00", "12:00", "16:00", "20:00"],
  "saturday": ["10:00", "14:00", "18:00"],
  "sunday": ["10:00", "14:00", "18:00"]
}
```

### Умеренный канал (развлечения)
```json
{
  "monday": ["09:00", "15:00", "21:00"],
  "tuesday": ["09:00", "15:00", "21:00"],
  "wednesday": ["09:00", "15:00", "21:00"],
  "thursday": ["09:00", "15:00", "21:00"],
  "friday": ["09:00", "15:00", "21:00"],
  "saturday": ["11:00", "17:00"],
  "sunday": ["11:00", "17:00"]
}
```

### Редкие публикации (бизнес)
```json
{
  "monday": ["09:00", "18:00"],
  "tuesday": ["09:00", "18:00"],
  "wednesday": ["09:00", "18:00"],
  "thursday": ["09:00", "18:00"],
  "friday": ["09:00", "18:00"],
  "saturday": ["12:00"],
  "sunday": []
}
```

## 🎨 Примеры кастомных промптов

### Для IT-канала
```
Ты - технический писатель в IT-сфере. Перепиши статью, адаптировав её для разработчиков.

Требования:
- Сохрани техническую точность
- Используй профессиональную терминологию
- Добавь практические примеры
- Структурируй информацию логично
- Сделай текст полезным для практики
```

### Для канала о здоровье
```
Ты - медицинский журналист. Перепиши материал о здоровье, сделав его понятным и полезным.

Требования:
- Сохрани медицинскую точность
- Используй простой язык
- Добавь практические советы
- Избегай самодиагностики
- Подчеркни важность консультации врача
```

### Для кулинарного канала
```
Ты - кулинарный блогер. Перепиши рецепт, сделав его более привлекательным и понятным.

Требования:
- Сохрани все ингредиенты и пропорции
- Упрости инструкции
- Добавь полезные советы
- Сделай описание аппетитным
- Укажи время приготовления
```

## 📊 Мониторинг эффективности

### Ключевые метрики
- **Процент публикации:** Сколько постов проходит модерацию
- **Время обработки ИИ:** Скорость переписывания
- **Активность каналов-доноров:** Количество новых постов
- **Качество контента:** Ручная оценка результатов

### Оптимизация
1. **Если много отклонений:** Улучшите черный список
2. **Если медленная обработка:** Смените модель ИИ
3. **Если плохое качество:** Настройте промпты
4. **Если мало контента:** Добавьте каналы-доноры

## 🚀 Продвинутые сценарии

### Мультиязычный канал
1. Добавьте каналы на разных языках
2. Настройте промпт для перевода
3. Используйте модель с поддержкой языков

### Тематические подборки
1. Создайте несколько целевых каналов по темам
2. Настройте фильтрацию по ключевым словам
3. Автоматически направляйте контент в нужные каналы

### Аналитика и отчеты
1. Настройте еженедельные отчеты
2. Отслеживайте популярные темы
3. Анализируйте эффективность каналов-доноров

---

**Помните:** Всегда соблюдайте авторские права и правила Telegram при использовании бота!
