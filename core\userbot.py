"""
Userbot для чтения постов из каналов-доноров
"""
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
import os

from pyrogram import Client, filters
from pyrogram.types import Message
from pyrogram.errors import FloodWait, ChannelPrivate, ChatAdminRequired
from loguru import logger

from config.settings import settings
from database.crud import DonorChannelCRUD, PostCRUD, LogCRUD
from database.models import DonorChannel
from utils.filters import content_filter
from core.openrouter_api import openrouter_api


class ContentMonsterUserbot:
    """Userbot для мониторинга каналов-доноров"""

    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
        self.client: Optional[Client] = None
        self.is_running = False
        self.monitored_channels: Dict[int, DonorChannel] = {}

        # Создаем клиент Pyrogram
        self.client = Client(
            name=settings.session_name,
            api_id=settings.api_id,
            api_hash=settings.api_hash,
            workdir="."
        )

        # Регистрируем обработчики
        self._register_handlers()

    def _register_handlers(self):
        """Регистрация обработчиков сообщений"""

        @self.client.on_message(filters.channel)
        async def handle_channel_message(client: Client, message: Message):
            """Обработчик новых сообщений в каналах"""
            try:
                await self._process_new_message(message)
            except Exception as e:
                logger.error(f"Ошибка обработки сообщения: {e}")

    async def start(self):
        """Запуск userbot"""
        try:
            logger.info("Запуск Content Monster Userbot...")

            await self.client.start()
            self.is_running = True

            # Загружаем список каналов для мониторинга
            await self._load_monitored_channels()

            logger.info(f"Userbot запущен. Мониторим {len(self.monitored_channels)} каналов")

        except Exception as e:
            logger.error(f"Ошибка запуска userbot: {e}")
            raise

    async def stop(self):
        """Остановка userbot"""
        try:
            logger.info("Остановка Content Monster Userbot...")
            self.is_running = False

            if self.client:
                await self.client.stop()

            logger.info("Userbot остановлен")

        except Exception as e:
            logger.error(f"Ошибка остановки userbot: {e}")

    async def _load_monitored_channels(self):
        """Загрузить список каналов для мониторинга"""
        async with self.db_session_factory() as db:
            try:
                channels = await DonorChannelCRUD.get_all_active(db)
                self.monitored_channels = {ch.channel_id: ch for ch in channels}

                logger.info(f"Загружено {len(channels)} каналов для мониторинга")

                # Проверяем доступ к каналам
                for channel in channels:
                    await self._check_channel_access(channel)

            except Exception as e:
                logger.error(f"Ошибка загрузки каналов: {e}")

    async def _check_channel_access(self, channel: DonorChannel) -> bool:
        """Проверить доступ к каналу"""
        try:
            chat = await self.client.get_chat(channel.channel_id)
            logger.info(f"Доступ к каналу {chat.title} ({channel.channel_id}) - OK")
            return True

        except ChannelPrivate:
            logger.warning(f"Канал {channel.channel_id} приватный или недоступен")
            return False
        except Exception as e:
            logger.error(f"Ошибка проверки доступа к каналу {channel.channel_id}: {e}")
            return False

    async def _process_new_message(self, message: Message):
        """Обработать новое сообщение из канала"""
        if not message.chat or message.chat.id not in self.monitored_channels:
            return

        channel = self.monitored_channels[message.chat.id]

        try:
            logger.info(f"Новое сообщение в канале {message.chat.title} (ID: {message.id})")

            # Проверяем, что это новое сообщение
            if message.id <= channel.last_post_id:
                return

            # Извлекаем контент
            text_content = self._extract_text(message)
            media_content = await self._extract_media(message)

            # Фильтруем контент
            is_allowed, reason = content_filter.is_post_allowed(
                text=text_content,
                media_count=len(media_content)
            )

            if not is_allowed:
                logger.info(f"Пост отклонен фильтром: {reason}")
                await self._update_last_post_id(channel.channel_id, message.id)
                return

            # Сохраняем пост в БД
            async with self.db_session_factory() as db:
                post = await PostCRUD.create(
                    db=db,
                    donor_channel_id=channel.id,
                    original_message_id=message.id,
                    original_text=text_content,
                    original_media=media_content
                )

                logger.info(f"Пост сохранен в БД (ID: {post.id})")

                # Логируем событие
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Новый пост из канала {message.chat.title}",
                    module="userbot",
                    post_id=post.id
                )

            # Обрабатываем текст через ИИ и определяем целевые каналы
            if text_content:
                await self._process_text_with_ai(post.id, text_content, channel.id)

            # Обновляем последний ID поста
            await self._update_last_post_id(channel.channel_id, message.id)

        except Exception as e:
            logger.error(f"Ошибка обработки сообщения {message.id}: {e}")

            async with self.db_session_factory() as db:
                await LogCRUD.add_log(
                    db=db,
                    level="ERROR",
                    message=f"Ошибка обработки сообщения: {e}",
                    module="userbot"
                )

    def _extract_text(self, message: Message) -> Optional[str]:
        """Извлечь текст из сообщения"""
        text_parts = []

        # Основной текст
        if message.text:
            text_parts.append(message.text)
        elif message.caption:
            text_parts.append(message.caption)

        # Текст из медиа-группы
        if hasattr(message, 'media_group_id') and message.media_group_id:
            # Для медиа-групп берем только первое сообщение
            pass

        return "\n".join(text_parts) if text_parts else None

    async def _extract_media(self, message: Message) -> List[Dict[str, Any]]:
        """Извлечь медиа из сообщения"""
        media_list = []

        try:
            # Фото
            if message.photo:
                media_list.append({
                    "type": "photo",
                    "file_id": message.photo.file_id,
                    "file_unique_id": message.photo.file_unique_id,
                    "width": message.photo.width,
                    "height": message.photo.height,
                    "file_size": getattr(message.photo, 'file_size', 0)
                })

            # Видео
            elif message.video:
                media_list.append({
                    "type": "video",
                    "file_id": message.video.file_id,
                    "file_unique_id": message.video.file_unique_id,
                    "width": message.video.width,
                    "height": message.video.height,
                    "duration": message.video.duration,
                    "file_size": getattr(message.video, 'file_size', 0),
                    "mime_type": getattr(message.video, 'mime_type', None)
                })

            # Аудио
            elif message.audio:
                media_list.append({
                    "type": "audio",
                    "file_id": message.audio.file_id,
                    "file_unique_id": message.audio.file_unique_id,
                    "duration": message.audio.duration,
                    "file_size": getattr(message.audio, 'file_size', 0),
                    "mime_type": getattr(message.audio, 'mime_type', None),
                    "title": getattr(message.audio, 'title', None),
                    "performer": getattr(message.audio, 'performer', None)
                })

            # Документ
            elif message.document:
                media_list.append({
                    "type": "document",
                    "file_id": message.document.file_id,
                    "file_unique_id": message.document.file_unique_id,
                    "file_name": getattr(message.document, 'file_name', None),
                    "file_size": getattr(message.document, 'file_size', 0),
                    "mime_type": getattr(message.document, 'mime_type', None)
                })

            # Анимация (GIF)
            elif message.animation:
                media_list.append({
                    "type": "animation",
                    "file_id": message.animation.file_id,
                    "file_unique_id": message.animation.file_unique_id,
                    "width": message.animation.width,
                    "height": message.animation.height,
                    "duration": message.animation.duration,
                    "file_size": getattr(message.animation, 'file_size', 0),
                    "mime_type": getattr(message.animation, 'mime_type', None)
                })

            # Стикер
            elif message.sticker:
                media_list.append({
                    "type": "sticker",
                    "file_id": message.sticker.file_id,
                    "file_unique_id": message.sticker.file_unique_id,
                    "width": message.sticker.width,
                    "height": message.sticker.height,
                    "is_animated": message.sticker.is_animated,
                    "file_size": getattr(message.sticker, 'file_size', 0)
                })

        except Exception as e:
            logger.error(f"Ошибка извлечения медиа: {e}")

        return media_list

    async def _process_text_with_ai(self, post_id: int, text: str, donor_channel_id: int):
        """Обработать текст через ИИ с учетом настроек целевых каналов"""
        try:
            logger.info(f"Обработка текста поста {post_id} через ИИ...")

            # Получаем связи с целевыми каналами
            async with self.db_session_factory() as db:
                from database.crud import ChannelMappingCRUD
                mappings = await ChannelMappingCRUD.get_mappings_for_donor(db, donor_channel_id)

                if not mappings:
                    logger.warning(f"Нет связей для канала-донора {donor_channel_id}")
                    # Используем стандартную обработку
                    await self._process_with_default_settings(post_id, text)
                    return

                # Обрабатываем для каждого целевого канала
                for mapping in mappings:
                    await self._process_for_target_channel(post_id, text, mapping)

        except Exception as e:
            logger.error(f"Ошибка обработки текста ИИ для поста {post_id}: {e}")

            async with self.db_session_factory() as db:
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="error"
                )

                await LogCRUD.add_log(
                    db=db,
                    level="ERROR",
                    message=f"Ошибка обработки ИИ: {e}",
                    module="userbot",
                    post_id=post_id
                )

    async def _process_with_default_settings(self, post_id: int, text: str):
        """Обработать с настройками по умолчанию"""
        try:
            # Очищаем текст
            cleaned_text = content_filter.clean_text(text)

            # Переписываем через ИИ
            async with openrouter_api:
                rewritten_text, processing_time = await openrouter_api.rewrite_text(
                    text=cleaned_text,
                    prompt_type="default"
                )

            # Добавляем подпись по умолчанию
            final_text = rewritten_text + settings.default_signature

            # Обновляем пост в БД
            async with self.db_session_factory() as db:
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="pending",
                    rewritten_text=rewritten_text,
                    final_text=final_text,
                    ai_model_used=settings.openrouter_model,
                    processing_time=processing_time
                )

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Текст обработан ИИ за {processing_time:.2f} сек (стандартные настройки)",
                    module="userbot",
                    post_id=post_id
                )

            logger.info(f"Пост {post_id} готов к модерации (стандартные настройки)")

        except Exception as e:
            logger.error(f"Ошибка стандартной обработки поста {post_id}: {e}")

    async def _process_for_target_channel(self, post_id: int, text: str, mapping):
        """Обработать текст для конкретного целевого канала"""
        try:
            target_channel = mapping.target_channel

            # Очищаем текст
            cleaned_text = content_filter.clean_text(text)

            # Определяем тип промпта
            prompt_type = target_channel.ai_prompt_type or "default"
            custom_prompt = mapping.custom_prompt

            # Переписываем через ИИ
            async with openrouter_api:
                if custom_prompt:
                    rewritten_text, processing_time = await openrouter_api.rewrite_text(
                        text=cleaned_text,
                        custom_prompt=custom_prompt
                    )
                else:
                    rewritten_text, processing_time = await openrouter_api.rewrite_text(
                        text=cleaned_text,
                        prompt_type=prompt_type
                    )

            # Добавляем индивидуальную подпись канала или стандартную
            signature = target_channel.custom_signature or settings.default_signature
            final_text = rewritten_text + signature

            # Создаем отдельную запись поста для этого канала
            async with self.db_session_factory() as db:
                # Создаем копию поста для конкретного целевого канала
                new_post = await PostCRUD.create(
                    db=db,
                    donor_channel_id=mapping.donor_channel_id,
                    original_message_id=0,  # Это копия
                    original_text=text,
                    original_media=[]
                )

                await PostCRUD.update_status(
                    db=db,
                    post_id=new_post.id,
                    status="pending",
                    rewritten_text=rewritten_text,
                    final_text=final_text,
                    ai_model_used=settings.openrouter_model,
                    processing_time=processing_time,
                    target_channel_id=target_channel.id
                )

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Пост обработан для канала {target_channel.title} за {processing_time:.2f} сек",
                    module="userbot",
                    post_id=new_post.id
                )

            logger.info(f"Пост обработан для канала {target_channel.title}")

        except Exception as e:
            logger.error(f"Ошибка обработки для канала {mapping.target_channel.title}: {e}")

    async def _update_last_post_id(self, channel_id: int, post_id: int):
        """Обновить ID последнего поста канала"""
        async with self.db_session_factory() as db:
            await DonorChannelCRUD.update_last_post_id(db, channel_id, post_id)

            # Обновляем локальный кэш
            if channel_id in self.monitored_channels:
                self.monitored_channels[channel_id].last_post_id = post_id

    async def add_channel(self, channel_username: str) -> tuple[bool, str]:
        """Добавить канал для мониторинга"""
        try:
            # Получаем информацию о канале
            chat = await self.client.get_chat(channel_username)

            if chat.type != "channel":
                return False, "Это не канал"

            # Проверяем лимит каналов
            if len(self.monitored_channels) >= settings.max_donor_channels:
                return False, f"Достигнут лимит каналов ({settings.max_donor_channels})"

            # Проверяем, не добавлен ли уже
            if chat.id in self.monitored_channels:
                return False, "Канал уже добавлен"

            # Добавляем в БД
            async with self.db_session_factory() as db:
                channel = await DonorChannelCRUD.create(
                    db=db,
                    channel_id=chat.id,
                    title=chat.title,
                    username=chat.username
                )

                # Добавляем в локальный кэш
                self.monitored_channels[chat.id] = channel

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Добавлен канал-донор: {chat.title}",
                    module="userbot"
                )

            logger.info(f"Канал {chat.title} добавлен для мониторинга")
            return True, f"Канал '{chat.title}' успешно добавлен"

        except ChannelPrivate:
            return False, "Канал приватный или недоступен"
        except Exception as e:
            logger.error(f"Ошибка добавления канала {channel_username}: {e}")
            return False, f"Ошибка: {e}"

    async def remove_channel(self, channel_id: int) -> tuple[bool, str]:
        """Удалить канал из мониторинга"""
        try:
            if channel_id not in self.monitored_channels:
                return False, "Канал не найден в списке мониторинга"

            channel = self.monitored_channels[channel_id]

            # Деактивируем в БД
            async with self.db_session_factory() as db:
                await DonorChannelCRUD.deactivate(db, channel_id)

                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Удален канал-донор: {channel.title}",
                    module="userbot"
                )

            # Удаляем из локального кэша
            del self.monitored_channels[channel_id]

            logger.info(f"Канал {channel.title} удален из мониторинга")
            return True, f"Канал '{channel.title}' удален"

        except Exception as e:
            logger.error(f"Ошибка удаления канала {channel_id}: {e}")
            return False, f"Ошибка: {e}"

    async def get_channel_info(self, channel_username: str) -> Optional[Dict[str, Any]]:
        """Получить информацию о канале"""
        try:
            chat = await self.client.get_chat(channel_username)

            return {
                "id": chat.id,
                "title": chat.title,
                "username": chat.username,
                "type": chat.type,
                "members_count": getattr(chat, 'members_count', 0),
                "description": getattr(chat, 'description', None)
            }

        except Exception as e:
            logger.error(f"Ошибка получения информации о канале {channel_username}: {e}")
            return None

    async def reload_channels(self):
        """Перезагрузить список каналов"""
        await self._load_monitored_channels()
        logger.info("Список каналов перезагружен")


# Глобальный экземпляр userbot (будет инициализирован в main.py)
userbot: Optional[ContentMonsterUserbot] = None
