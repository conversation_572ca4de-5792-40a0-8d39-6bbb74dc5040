"""
Обработчик медиа файлов для Content Monster GPT Bot
"""
import os
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import aiohttp
from loguru import logger

from config.settings import settings


class MediaHandler:
    """Класс для работы с медиа файлами"""

    def __init__(self):
        self.download_dir = Path("downloads")
        self.download_dir.mkdir(exist_ok=True)
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    def validate_media_size(self, file_size: int) -> bool:
        """Проверить размер файла"""
        max_size_bytes = settings.max_file_size * 1024 * 1024  # MB to bytes
        return file_size <= max_size_bytes

    def validate_media_type(self, media_type: str) -> bool:
        """Проверить тип медиа"""
        return media_type in settings.get_supported_media_types()

    def get_file_extension(self, mime_type: str) -> str:
        """Получить расширение файла по MIME типу"""
        mime_to_ext = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp',
            'video/mp4': '.mp4',
            'video/avi': '.avi',
            'video/mov': '.mov',
            'video/webm': '.webm',
            'audio/mp3': '.mp3',
            'audio/wav': '.wav',
            'audio/ogg': '.ogg',
            'audio/m4a': '.m4a',
            'application/pdf': '.pdf',
            'application/zip': '.zip',
            'text/plain': '.txt'
        }
        return mime_to_ext.get(mime_type, '.bin')

    async def download_file(self, file_url: str, filename: str) -> Optional[str]:
        """Скачать файл по URL"""
        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            file_path = self.download_dir / filename

            async with self.session.get(file_url) as response:
                if response.status == 200:
                    with open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

                    logger.info(f"Файл скачан: {filename}")
                    return str(file_path)
                else:
                    logger.error(f"Ошибка скачивания файла: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"Ошибка скачивания файла {filename}: {e}")
            return None

    def cleanup_old_files(self, max_age_hours: int = 24):
        """Очистить старые файлы"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600

            for file_path in self.download_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        logger.info(f"Удален старый файл: {file_path.name}")

        except Exception as e:
            logger.error(f"Ошибка очистки файлов: {e}")

    def get_media_info(self, media_data: Dict[str, Any]) -> Dict[str, Any]:
        """Получить информацию о медиа"""
        info = {
            "type": media_data.get("type", "unknown"),
            "file_id": media_data.get("file_id"),
            "file_size": media_data.get("file_size", 0),
            "mime_type": media_data.get("mime_type"),
            "is_valid_size": True,
            "is_valid_type": True
        }

        # Проверяем размер
        if info["file_size"] > 0:
            info["is_valid_size"] = self.validate_media_size(info["file_size"])

        # Проверяем тип
        if info["type"]:
            info["is_valid_type"] = self.validate_media_type(info["type"])

        # Добавляем дополнительную информацию в зависимости от типа
        if info["type"] == "photo":
            info.update({
                "width": media_data.get("width", 0),
                "height": media_data.get("height", 0)
            })
        elif info["type"] in ["video", "animation"]:
            info.update({
                "width": media_data.get("width", 0),
                "height": media_data.get("height", 0),
                "duration": media_data.get("duration", 0)
            })
        elif info["type"] == "audio":
            info.update({
                "duration": media_data.get("duration", 0),
                "title": media_data.get("title"),
                "performer": media_data.get("performer")
            })
        elif info["type"] == "document":
            info.update({
                "file_name": media_data.get("file_name")
            })

        return info

    def format_media_info(self, media_info: Dict[str, Any]) -> str:
        """Форматировать информацию о медиа для отображения"""
        media_type = media_info.get("type", "unknown")
        file_size = media_info.get("file_size", 0)

        # Форматируем размер файла
        if file_size > 0:
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
        else:
            size_str = "неизвестно"

        # Базовая информация
        info_parts = [f"Тип: {media_type}", f"Размер: {size_str}"]

        # Дополнительная информация
        if media_type in ["photo", "video", "animation"]:
            width = media_info.get("width", 0)
            height = media_info.get("height", 0)
            if width and height:
                info_parts.append(f"Разрешение: {width}x{height}")

        if media_type in ["video", "animation", "audio"]:
            duration = media_info.get("duration", 0)
            if duration:
                minutes = duration // 60
                seconds = duration % 60
                info_parts.append(f"Длительность: {minutes:02d}:{seconds:02d}")

        if media_type == "audio":
            title = media_info.get("title")
            performer = media_info.get("performer")
            if title:
                info_parts.append(f"Название: {title}")
            if performer:
                info_parts.append(f"Исполнитель: {performer}")

        if media_type == "document":
            file_name = media_info.get("file_name")
            if file_name:
                info_parts.append(f"Имя файла: {file_name}")

        return " | ".join(info_parts)

    def process_media_group(self, media_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Обработать группу медиа файлов"""
        if not media_list:
            return {
                "total_count": 0,
                "valid_count": 0,
                "total_size": 0,
                "types": [],
                "is_valid": True
            }

        total_size = 0
        valid_count = 0
        types = []

        for media in media_list:
            media_info = self.get_media_info(media)

            if media_info["is_valid_size"] and media_info["is_valid_type"]:
                valid_count += 1

            total_size += media_info["file_size"]

            media_type = media_info["type"]
            if media_type not in types:
                types.append(media_type)

        return {
            "total_count": len(media_list),
            "valid_count": valid_count,
            "total_size": total_size,
            "types": types,
            "is_valid": valid_count == len(media_list)
        }

    async def close(self):
        """Закрыть сессию"""
        if self.session:
            await self.session.close()


# Глобальный экземпляр обработчика медиа
media_handler = MediaHandler()
