# ❓ Часто задаваемые вопросы (FAQ)

## 🚀 Установка и запуск

### Q: Какая версия Python нужна?
**A:** Python 3.9 или выше. Рекомендуется Python 3.11+.

### Q: Не устанавливается tgcrypto, что делать?
**A:** tgcrypto опционален. Бот работает без него, но медленнее. Для установки нужен компилятор C++:
- Windows: Visual Studio Build Tools
- Linux: `sudo apt install build-essential`
- macOS: Xcode Command Line Tools

### Q: Ошибка "API недоступен" при запуске
**A:** Проверьте:
1. Интернет-соединение
2. Правильность API ключей в .env
3. Баланс на OpenRouter.ai
4. Доступность сервисов Telegram

### Q: Бот не отвечает на команды
**A:** Убедитесь, что:
1. BOT_TOKEN правильный
2. Ваш ADMIN_ID указан верно
3. Бот запущен и работает
4. Нет ошибок в логах

## 🔑 Настройка API

### Q: Где получить API_ID и API_HASH?
**A:** 
1. Перейдите на https://my.telegram.org
2. Войдите в аккаунт Telegram
3. Создайте новое приложение в "API development tools"
4. Скопируйте api_id (число) и api_hash (строка)

### Q: Как получить OpenRouter API ключ?
**A:**
1. Зарегистрируйтесь на https://openrouter.ai
2. Пополните баланс ($5-10 достаточно)
3. Создайте API ключ в разделе "Keys"
4. Скопируйте ключ формата `sk-or-v1-...`

### Q: Сколько стоит использование OpenRouter?
**A:** Цены зависят от модели:
- GPT-3.5-turbo: ~$0.001 за 1000 токенов
- GPT-4: ~$0.03 за 1000 токенов
- Claude-3: ~$0.015 за 1000 токенов

Для среднего канала (50 постов/день) расходы составят $5-20/месяц.

### Q: Можно ли использовать другие ИИ модели?
**A:** Да, OpenRouter поддерживает множество моделей. Популярные:
- `openai/gpt-4-turbo-preview` (лучшее качество)
- `openai/gpt-3.5-turbo` (быстро и дешево)
- `anthropic/claude-3-sonnet` (хорошо для творчества)
- `mistralai/mistral-7b-instruct` (бюджетный вариант)

## 📱 Работа с каналами

### Q: Userbot не может читать канал
**A:** Проверьте:
1. Подписаны ли вы на канал своим аккаунтом
2. Канал не приватный или у вас есть доступ
3. API_ID и API_HASH правильные
4. Аккаунт не заблокирован

### Q: Бот не может публиковать в канал
**A:** Убедитесь, что:
1. Бот добавлен в администраторы канала
2. У бота есть права на публикацию сообщений
3. Канал не ограничивает публикации
4. BOT_TOKEN правильный

### Q: Можно ли добавить больше 5 каналов-доноров?
**A:** По умолчанию лимит 5 каналов. Можно изменить в настройках:
```env
MAX_DONOR_CHANNELS=10
```

### Q: Как работать с приватными каналами?
**A:** Для приватных каналов нужно:
1. Быть участником канала
2. Иметь права на чтение сообщений
3. Использовать ID канала вместо username

## 🤖 Работа с ИИ

### Q: ИИ плохо переписывает тексты
**A:** Попробуйте:
1. Изменить температуру (0.3-0.9)
2. Настроить кастомный промпт
3. Сменить модель ИИ
4. Увеличить количество токенов

### Q: Как настроить промпт для своей тематики?
**A:** В настройках бота выберите тип промпта или создайте кастомный:
```
Ты - эксперт в [ВАШЕЙ ОБЛАСТИ]. Перепиши текст, адаптировав для [ВАШЕЙ АУДИТОРИИ].

Требования:
- [ВАШИ ТРЕБОВАНИЯ]
- [СТИЛЬ НАПИСАНИЯ]
- [ОСОБЕННОСТИ]
```

### Q: Можно ли отключить ИИ обработку?
**A:** Нет, ИИ обработка - основная функция бота. Но можно:
1. Использовать простой промпт
2. Установить низкую температуру (0.1)
3. Редактировать тексты вручную

### Q: Ошибка "Не удалось переписать текст"
**A:** Возможные причины:
1. Закончился баланс на OpenRouter
2. Превышен лимит запросов
3. Проблемы с интернетом
4. Неправильный API ключ

## 📊 Модерация и публикация

### Q: Как настроить автоматическую публикацию?
**A:** В настройках включите автопубликацию и установите:
1. Интервал между постами
2. Максимум постов в час
3. Расписание по дням недели

### Q: Можно ли публиковать без модерации?
**A:** Не рекомендуется, но можно настроить автопубликацию. Лучше использовать хорошие фильтры и черный список.

### Q: Как работает черный список?
**A:** Бот проверяет текст на наличие запрещенных слов. Если находит - отклоняет пост. Поддерживает:
- Точные совпадения слов
- Регистронезависимый поиск
- Границы слов

### Q: Посты не проходят фильтрацию
**A:** Проверьте:
1. Черный список (возможно, слишком строгий)
2. Минимальную длину текста
3. Настройки фильтра спама
4. Логи для понимания причин отклонения

## 🔧 Техническая поддержка

### Q: Где посмотреть логи?
**A:** Логи сохраняются в папке `logs/`:
- `content_monster.log` - основные логи
- `errors.log` - только ошибки

### Q: Бот перестал работать
**A:** Проверьте:
1. Логи на наличие ошибок
2. Процесс запущен
3. Интернет-соединение
4. Баланс API сервисов

### Q: Как обновить бот?
**A:**
1. Скачайте новую версию
2. Сохраните .env файл
3. Обновите зависимости: `pip install -r requirements.txt --upgrade`
4. Перезапустите бот

### Q: Можно ли запустить несколько ботов?
**A:** Да, но нужны разные:
1. BOT_TOKEN для каждого бота
2. SESSION_NAME для userbot
3. Папки проекта
4. Порты (если используете веб-интерфейс)

## 💾 База данных

### Q: Где хранятся данные?
**A:** В файле `content_monster.db` (SQLite) в корне проекта.

### Q: Как сделать резервную копию?
**A:** Скопируйте файл `content_monster.db` в безопасное место.

### Q: Можно ли использовать PostgreSQL?
**A:** Да, измените DATABASE_URL в .env:
```env
DATABASE_URL=postgresql+asyncpg://user:password@localhost/content_monster
```

### Q: Как очистить старые данные?
**A:** В настройках бота есть функция очистки или вручную:
```sql
DELETE FROM log_entries WHERE created_at < date('now', '-30 days');
DELETE FROM posts WHERE status = 'rejected' AND created_at < date('now', '-7 days');
```

## 🛡️ Безопасность

### Q: Безопасно ли использовать бот?
**A:** Да, при соблюдении правил:
1. Не передавайте API ключи третьим лицам
2. Используйте сильные пароли
3. Регулярно обновляйте бот
4. Следите за логами

### Q: Может ли Telegram заблокировать аккаунт?
**A:** Риск минимален при разумном использовании:
1. Не спамьте
2. Соблюдайте лимиты
3. Не нарушайте авторские права
4. Используйте задержки между действиями

### Q: Как защитить API ключи?
**A:**
1. Никогда не публикуйте .env файл
2. Используйте переменные окружения
3. Ограничьте доступ к серверу
4. Регулярно меняйте ключи

## 📈 Оптимизация

### Q: Как увеличить скорость работы?
**A:**
1. Установите tgcrypto
2. Используйте быстрые модели ИИ
3. Оптимизируйте промпты
4. Увеличьте лимиты API

### Q: Как снизить расходы на ИИ?
**A:**
1. Используйте дешевые модели (GPT-3.5, Mistral)
2. Сократите максимум токенов
3. Оптимизируйте промпты
4. Фильтруйте контент до ИИ обработки

### Q: Бот работает медленно
**A:** Возможные причины:
1. Медленная модель ИИ
2. Большие медиа файлы
3. Много каналов-доноров
4. Слабый интернет

---

**Не нашли ответ?** Проверьте логи в папке `logs/` или создайте issue в репозитории проекта.
