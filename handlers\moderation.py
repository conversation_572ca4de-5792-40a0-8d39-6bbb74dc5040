"""
Обработчики модерации постов
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from loguru import logger

from database.crud import PostCRUD, TargetChannelCRUD, LogCRUD
from database.models import Post
from utils.media_handler import media_handler
from utils.filters import content_filter


class ModerationHandlers:
    """Обработчики модерации постов"""
    
    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
    
    async def get_moderation_queue(self, limit: int = 10) -> List[Post]:
        """Получить очередь постов на модерацию"""
        async with self.db_session_factory() as db:
            try:
                posts = await PostCRUD.get_pending_posts(db)
                return posts[:limit]
                
            except Exception as e:
                logger.error(f"Ошибка получения очереди модерации: {e}")
                return []
    
    async def get_post_details(self, post_id: int) -> Optional[Dict[str, Any]]:
        """Получить детальную информацию о посте"""
        async with self.db_session_factory() as db:
            try:
                post = await PostCRUD.get_by_id(db, post_id)
                if not post:
                    return None
                
                # Анализируем медиа
                media_info = None
                if post.original_media:
                    media_info = media_handler.process_media_group(post.original_media)
                
                # Анализируем текст
                text_stats = None
                if post.original_text:
                    text_stats = content_filter.get_text_stats(post.original_text)
                
                return {
                    "post": post,
                    "media_info": media_info,
                    "text_stats": text_stats,
                    "processing_time": post.processing_time or 0,
                    "ai_model": post.ai_model_used or "неизвестно"
                }
                
            except Exception as e:
                logger.error(f"Ошибка получения деталей поста {post_id}: {e}")
                return None
    
    def format_post_for_moderation(self, post_details: Dict[str, Any]) -> str:
        """Форматировать пост для модерации"""
        post = post_details["post"]
        media_info = post_details.get("media_info")
        text_stats = post_details.get("text_stats")
        
        text = f"📝 <b>Пост #{post.id} для модерации</b>\n\n"
        
        # Информация об источнике
        text += f"📥 <b>Источник:</b> {post.donor_channel.title}\n"
        text += f"🕐 <b>Получен:</b> {post.created_at.strftime('%d.%m.%Y %H:%M')}\n"
        
        # Информация об ИИ обработке
        if post.ai_model_used:
            text += f"🧠 <b>Модель ИИ:</b> {post.ai_model_used}\n"
        if post.processing_time:
            text += f"⏱ <b>Время обработки:</b> {post.processing_time:.2f} сек\n"
        
        text += "\n"
        
        # Статистика текста
        if text_stats:
            text += f"📊 <b>Статистика:</b> {text_stats['words']} слов, {text_stats['length']} символов\n"
            if text_stats['hashtags'] > 0:
                text += f"🏷 <b>Хештеги:</b> {text_stats['hashtags']}\n"
            if text_stats['mentions'] > 0:
                text += f"👥 <b>Упоминания:</b> {text_stats['mentions']}\n"
            if text_stats['urls'] > 0:
                text += f"🔗 <b>Ссылки:</b> {text_stats['urls']}\n"
        
        # Информация о медиа
        if media_info and media_info['total_count'] > 0:
            text += f"📎 <b>Медиа:</b> {media_info['total_count']} файлов"
            if media_info['total_size'] > 0:
                size_mb = media_info['total_size'] / (1024 * 1024)
                text += f" ({size_mb:.1f} MB)"
            text += f"\n📋 <b>Типы:</b> {', '.join(media_info['types'])}\n"
        
        text += "\n"
        
        # Оригинальный текст
        if post.original_text:
            original_preview = post.original_text[:300]
            if len(post.original_text) > 300:
                original_preview += "..."
            text += f"<b>📄 Оригинальный текст:</b>\n<code>{original_preview}</code>\n\n"
        
        # Переписанный текст
        if post.rewritten_text:
            rewritten_preview = post.rewritten_text[:300]
            if len(post.rewritten_text) > 300:
                rewritten_preview += "..."
            text += f"<b>✏️ Переписанный текст:</b>\n<code>{rewritten_preview}</code>\n\n"
        
        # Финальный текст с подписью
        if post.final_text:
            final_preview = post.final_text[:300]
            if len(post.final_text) > 300:
                final_preview += "..."
            text += f"<b>📤 Финальный текст:</b>\n<code>{final_preview}</code>"
        
        return text
    
    def get_moderation_keyboard(self, post_id: int, show_channels: bool = False) -> InlineKeyboardMarkup:
        """Получить клавиатуру модерации"""
        if show_channels:
            # Показываем выбор каналов для публикации
            return InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔙 Назад к посту", callback_data=f"moderate_post:{post_id}")
                ]
            ])
        else:
            # Основные действия модерации
            return InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="✅ Одобрить", callback_data=f"approve:{post_id}"),
                    InlineKeyboardButton(text="✏️ Редактировать", callback_data=f"edit:{post_id}")
                ],
                [
                    InlineKeyboardButton(text="❌ Отклонить", callback_data=f"reject:{post_id}"),
                    InlineKeyboardButton(text="📋 Детали", callback_data=f"details:{post_id}")
                ],
                [
                    InlineKeyboardButton(text="⏭ Следующий", callback_data="next_post"),
                    InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
                ]
            ])
    
    async def get_target_channels_keyboard(self, post_id: int) -> InlineKeyboardMarkup:
        """Получить клавиатуру выбора целевых каналов"""
        async with self.db_session_factory() as db:
            try:
                channels = await TargetChannelCRUD.get_all_active(db)
                
                keyboard_buttons = []
                
                for channel in channels:
                    keyboard_buttons.append([
                        InlineKeyboardButton(
                            text=f"📤 {channel.title}",
                            callback_data=f"publish:{post_id}:{channel.id}"
                        )
                    ])
                
                keyboard_buttons.append([
                    InlineKeyboardButton(text="🔙 Назад", callback_data=f"moderate_post:{post_id}")
                ])
                
                return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
                
            except Exception as e:
                logger.error(f"Ошибка получения каналов: {e}")
                return InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="❌ Ошибка", callback_data="moderation")]
                ])
    
    async def schedule_post(self, post_id: int, target_channel_id: int, publish_time: datetime) -> bool:
        """Запланировать публикацию поста"""
        async with self.db_session_factory() as db:
            try:
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="approved",
                    target_channel_id=target_channel_id,
                    scheduled_for=publish_time
                )
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Пост #{post_id} запланирован к публикации на {publish_time}",
                    module="moderation",
                    post_id=post_id
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Ошибка планирования поста {post_id}: {e}")
                return False
    
    async def reject_post(self, post_id: int, reason: str = None) -> bool:
        """Отклонить пост"""
        async with self.db_session_factory() as db:
            try:
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="rejected"
                )
                
                log_message = f"Пост #{post_id} отклонен"
                if reason:
                    log_message += f". Причина: {reason}"
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=log_message,
                    module="moderation",
                    post_id=post_id
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Ошибка отклонения поста {post_id}: {e}")
                return False
    
    async def update_post_text(self, post_id: int, new_text: str) -> bool:
        """Обновить текст поста"""
        async with self.db_session_factory() as db:
            try:
                from config.settings import settings
                
                final_text = new_text + settings.default_signature
                
                await PostCRUD.update_status(
                    db=db,
                    post_id=post_id,
                    status="pending",
                    rewritten_text=new_text,
                    final_text=final_text
                )
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Текст поста #{post_id} обновлен",
                    module="moderation",
                    post_id=post_id
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Ошибка обновления текста поста {post_id}: {e}")
                return False
    
    async def get_moderation_statistics(self) -> Dict[str, Any]:
        """Получить статистику модерации"""
        async with self.db_session_factory() as db:
            try:
                from sqlalchemy import select, func
                from database.models import Post
                
                # Общая статистика
                total_result = await db.execute(select(func.count(Post.id)))
                total_posts = total_result.scalar() or 0
                
                # По статусам
                stats = {}
                for status in ["pending", "approved", "published", "rejected"]:
                    result = await db.execute(
                        select(func.count(Post.id)).where(Post.status == status)
                    )
                    stats[status] = result.scalar() or 0
                
                # Статистика за сегодня
                today = datetime.now().date()
                tomorrow = today + timedelta(days=1)
                
                today_result = await db.execute(
                    select(func.count(Post.id)).where(
                        Post.created_at >= today,
                        Post.created_at < tomorrow
                    )
                )
                today_posts = today_result.scalar() or 0
                
                return {
                    "total_posts": total_posts,
                    "pending": stats["pending"],
                    "approved": stats["approved"],
                    "published": stats["published"],
                    "rejected": stats["rejected"],
                    "today_posts": today_posts,
                    "approval_rate": (stats["published"] / max(total_posts, 1)) * 100
                }
                
            except Exception as e:
                logger.error(f"Ошибка получения статистики модерации: {e}")
                return {}
    
    def format_moderation_stats(self, stats: Dict[str, Any]) -> str:
        """Форматировать статистику модерации"""
        text = "📊 <b>Статистика модерации</b>\n\n"
        
        text += f"📝 Всего постов: {stats.get('total_posts', 0)}\n"
        text += f"⏳ На модерации: {stats.get('pending', 0)}\n"
        text += f"✅ Одобрено: {stats.get('approved', 0)}\n"
        text += f"📤 Опубликовано: {stats.get('published', 0)}\n"
        text += f"❌ Отклонено: {stats.get('rejected', 0)}\n\n"
        
        text += f"📅 Сегодня: {stats.get('today_posts', 0)} постов\n"
        text += f"📈 Процент публикации: {stats.get('approval_rate', 0):.1f}%"
        
        return text


# Глобальный экземпляр обработчиков модерации
moderation_handlers: ModerationHandlers = None
