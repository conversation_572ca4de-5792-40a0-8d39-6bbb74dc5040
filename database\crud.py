"""
CRUD операции для базы данных
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload

from .models import (
    DonorChannel, TargetChannel, Post, BlacklistWord,
    BotSettings, UserSession, LogEntry, ChannelMapping
)


class DonorChannelCRUD:
    """CRUD операции для каналов-доноров"""

    @staticmethod
    async def create(db: AsyncSession, channel_id: int, title: str, username: str = None) -> DonorChannel:
        """Создать канал-донор"""
        channel = DonorChannel(
            channel_id=channel_id,
            title=title,
            username=username
        )
        db.add(channel)
        await db.commit()
        await db.refresh(channel)
        return channel

    @staticmethod
    async def get_by_id(db: AsyncSession, channel_id: int) -> Optional[DonorChannel]:
        """Получить канал по ID"""
        result = await db.execute(
            select(DonorChannel).where(DonorChannel.channel_id == channel_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_all_active(db: AsyncSession) -> List[DonorChannel]:
        """Получить все активные каналы-доноры"""
        result = await db.execute(
            select(DonorChannel).where(DonorChannel.is_active == True)
        )
        return result.scalars().all()

    @staticmethod
    async def update_last_post_id(db: AsyncSession, channel_id: int, last_post_id: int):
        """Обновить ID последнего поста"""
        await db.execute(
            update(DonorChannel)
            .where(DonorChannel.channel_id == channel_id)
            .values(last_post_id=last_post_id)
        )
        await db.commit()

    @staticmethod
    async def deactivate(db: AsyncSession, channel_id: int):
        """Деактивировать канал"""
        await db.execute(
            update(DonorChannel)
            .where(DonorChannel.channel_id == channel_id)
            .values(is_active=False)
        )
        await db.commit()


class TargetChannelCRUD:
    """CRUD операции для целевых каналов"""

    @staticmethod
    async def create(
        db: AsyncSession,
        channel_id: int,
        title: str,
        username: str = None,
        custom_signature: str = None,
        ai_prompt_type: str = "default",
        post_interval: int = None,
        max_posts_per_day: int = 10
    ) -> TargetChannel:
        """Создать целевой канал"""
        channel = TargetChannel(
            channel_id=channel_id,
            title=title,
            username=username,
            custom_signature=custom_signature,
            ai_prompt_type=ai_prompt_type,
            post_interval=post_interval,
            max_posts_per_day=max_posts_per_day
        )
        db.add(channel)
        await db.commit()
        await db.refresh(channel)
        return channel

    @staticmethod
    async def get_all_active(db: AsyncSession) -> List[TargetChannel]:
        """Получить все активные целевые каналы"""
        result = await db.execute(
            select(TargetChannel).where(TargetChannel.is_active == True)
        )
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, channel_id: int) -> Optional[TargetChannel]:
        """Получить целевой канал по ID"""
        result = await db.execute(
            select(TargetChannel).where(TargetChannel.id == channel_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def update_settings(
        db: AsyncSession,
        channel_id: int,
        custom_signature: str = None,
        ai_prompt_type: str = None,
        post_interval: int = None,
        max_posts_per_day: int = None
    ) -> bool:
        """Обновить настройки целевого канала"""
        try:
            update_data = {}
            if custom_signature is not None:
                update_data["custom_signature"] = custom_signature
            if ai_prompt_type is not None:
                update_data["ai_prompt_type"] = ai_prompt_type
            if post_interval is not None:
                update_data["post_interval"] = post_interval
            if max_posts_per_day is not None:
                update_data["max_posts_per_day"] = max_posts_per_day

            if update_data:
                await db.execute(
                    update(TargetChannel)
                    .where(TargetChannel.channel_id == channel_id)
                    .values(**update_data)
                )
                await db.commit()


class ChannelMappingCRUD:
    """CRUD операции для связей донор-целевой канал"""

    @staticmethod
    async def create(
        db: AsyncSession,
        donor_channel_id: int,
        target_channel_id: int,
        custom_prompt: str = None,
        priority: int = 1
    ) -> ChannelMapping:
        """Создать связь донор-целевой канал"""
        from .models import ChannelMapping

        mapping = ChannelMapping(
            donor_channel_id=donor_channel_id,
            target_channel_id=target_channel_id,
            custom_prompt=custom_prompt,
            priority=priority
        )
        db.add(mapping)
        await db.commit()
        await db.refresh(mapping)
        return mapping

    @staticmethod
    async def get_mappings_for_donor(db: AsyncSession, donor_channel_id: int) -> List[ChannelMapping]:
        """Получить все связи для канала-донора"""
        from .models import ChannelMapping

        result = await db.execute(
            select(ChannelMapping)
            .options(selectinload(ChannelMapping.target_channel))
            .where(
                and_(
                    ChannelMapping.donor_channel_id == donor_channel_id,
                    ChannelMapping.is_active == True
                )
            )
            .order_by(ChannelMapping.priority)
        )
        return result.scalars().all()

    @staticmethod
    async def get_mappings_for_target(db: AsyncSession, target_channel_id: int) -> List[ChannelMapping]:
        """Получить все связи для целевого канала"""
        from .models import ChannelMapping

        result = await db.execute(
            select(ChannelMapping)
            .options(selectinload(ChannelMapping.donor_channel))
            .where(
                and_(
                    ChannelMapping.target_channel_id == target_channel_id,
                    ChannelMapping.is_active == True
                )
            )
            .order_by(ChannelMapping.priority)
        )
        return result.scalars().all()

    @staticmethod
    async def delete_mapping(db: AsyncSession, donor_channel_id: int, target_channel_id: int) -> bool:
        """Удалить связь"""
        from .models import ChannelMapping

        try:
            await db.execute(
                update(ChannelMapping)
                .where(
                    and_(
                        ChannelMapping.donor_channel_id == donor_channel_id,
                        ChannelMapping.target_channel_id == target_channel_id
                    )
                )
                .values(is_active=False)
            )
            await db.commit()
            return True
        except Exception:
            return False


class PostCRUD:
    """CRUD операции для постов"""

    @staticmethod
    async def create(
        db: AsyncSession,
        donor_channel_id: int,
        original_message_id: int,
        original_text: str = None,
        original_media: List[Dict] = None
    ) -> Post:
        """Создать новый пост"""
        post = Post(
            donor_channel_id=donor_channel_id,
            original_message_id=original_message_id,
            original_text=original_text,
            original_media=original_media or []
        )
        db.add(post)
        await db.commit()
        await db.refresh(post)
        return post

    @staticmethod
    async def get_pending_posts(db: AsyncSession) -> List[Post]:
        """Получить посты ожидающие модерации"""
        result = await db.execute(
            select(Post)
            .options(selectinload(Post.donor_channel))
            .where(Post.status == "pending")
            .order_by(Post.created_at)
        )
        return result.scalars().all()

    @staticmethod
    async def update_status(db: AsyncSession, post_id: int, status: str, **kwargs):
        """Обновить статус поста"""
        update_data = {"status": status}

        if status == "approved":
            update_data["processed_at"] = datetime.now()
        elif status == "published":
            update_data["published_at"] = datetime.now()

        update_data.update(kwargs)

        await db.execute(
            update(Post)
            .where(Post.id == post_id)
            .values(**update_data)
        )
        await db.commit()

    @staticmethod
    async def get_scheduled_posts(db: AsyncSession) -> List[Post]:
        """Получить посты для публикации по расписанию"""
        now = datetime.now()
        result = await db.execute(
            select(Post)
            .options(selectinload(Post.target_channel))
            .where(
                and_(
                    Post.status == "approved",
                    Post.scheduled_for <= now
                )
            )
            .order_by(Post.scheduled_for)
        )
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, post_id: int) -> Optional[Post]:
        """Получить пост по ID"""
        result = await db.execute(
            select(Post)
            .options(selectinload(Post.donor_channel), selectinload(Post.target_channel))
            .where(Post.id == post_id)
        )
        return result.scalar_one_or_none()


class BlacklistCRUD:
    """CRUD операции для черного списка"""

    @staticmethod
    async def add_word(db: AsyncSession, word: str) -> BlacklistWord:
        """Добавить слово в черный список"""
        blacklist_word = BlacklistWord(word=word.lower())
        db.add(blacklist_word)
        await db.commit()
        await db.refresh(blacklist_word)
        return blacklist_word

    @staticmethod
    async def get_all_active(db: AsyncSession) -> List[str]:
        """Получить все активные слова черного списка"""
        result = await db.execute(
            select(BlacklistWord.word).where(BlacklistWord.is_active == True)
        )
        return [word for word in result.scalars().all()]

    @staticmethod
    async def remove_word(db: AsyncSession, word: str):
        """Удалить слово из черного списка"""
        await db.execute(
            delete(BlacklistWord).where(BlacklistWord.word == word.lower())
        )
        await db.commit()


class SettingsCRUD:
    """CRUD операции для настроек"""

    @staticmethod
    async def get_setting(db: AsyncSession, key: str) -> Optional[str]:
        """Получить значение настройки"""
        result = await db.execute(
            select(BotSettings.value).where(BotSettings.key == key)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def set_setting(db: AsyncSession, key: str, value: str, description: str = None):
        """Установить значение настройки"""
        # Проверяем, существует ли настройка
        existing = await db.execute(
            select(BotSettings).where(BotSettings.key == key)
        )
        setting = existing.scalar_one_or_none()

        if setting:
            # Обновляем существующую
            await db.execute(
                update(BotSettings)
                .where(BotSettings.key == key)
                .values(value=value, updated_at=datetime.now())
            )
        else:
            # Создаем новую
            new_setting = BotSettings(key=key, value=value, description=description)
            db.add(new_setting)

        await db.commit()


class LogCRUD:
    """CRUD операции для логов"""

    @staticmethod
    async def add_log(
        db: AsyncSession,
        level: str,
        message: str,
        module: str = None,
        user_id: int = None,
        post_id: int = None
    ):
        """Добавить запись в лог"""
        log_entry = LogEntry(
            level=level,
            message=message,
            module=module,
            user_id=user_id,
            post_id=post_id
        )
        db.add(log_entry)
        await db.commit()
