"""
Инициализация и настройка базы данных
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool
from loguru import logger

from config.settings import settings
from .models import Base


class DatabaseManager:
    """Менеджер базы данных"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
    
    async def init_database(self):
        """Инициализация базы данных"""
        try:
            # Создаем движок
            self.engine = create_async_engine(
                settings.database_url,
                echo=False,  # Установить True для отладки SQL запросов
                poolclass=StaticPool if "sqlite" in settings.database_url else None,
                connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
            )
            
            # Создаем фабрику сессий
            self.session_factory = async_sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Создаем таблицы
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            logger.info("База данных инициализирована успешно")
            
        except Exception as e:
            logger.error(f"Ошибка инициализации базы данных: {e}")
            raise
    
    async def close_database(self):
        """Закрытие соединения с базой данных"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Соединение с базой данных закрыто")
    
    def get_session(self):
        """Получить сессию базы данных"""
        if not self.session_factory:
            raise RuntimeError("База данных не инициализирована")
        return self.session_factory()


# Глобальный экземпляр менеджера БД
db_manager = DatabaseManager()
