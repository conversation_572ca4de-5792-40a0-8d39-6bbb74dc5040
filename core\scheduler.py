"""
Планировщик для автоматической публикации постов
"""
import asyncio
from datetime import datetime, timedelta
from typing import Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from loguru import logger

from config.settings import settings
from database.crud import PostCRUD, LogCRUD


class ContentScheduler:
    """Планировщик контента"""
    
    def __init__(self, db_session_factory, telegram_bot):
        self.db_session_factory = db_session_factory
        self.telegram_bot = telegram_bot
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
    
    async def start(self):
        """Запуск планировщика"""
        try:
            logger.info("Запуск планировщика контента...")
            
            # Добавляем задачу проверки постов для публикации
            self.scheduler.add_job(
                self._check_scheduled_posts,
                trigger=IntervalTrigger(minutes=1),  # Проверяем каждую минуту
                id="check_scheduled_posts",
                name="Проверка запланированных постов"
            )
            
            # Добавляем задачу очистки старых логов
            self.scheduler.add_job(
                self._cleanup_old_logs,
                trigger=CronTrigger(hour=3, minute=0),  # Каждый день в 3:00
                id="cleanup_logs",
                name="Очистка старых логов"
            )
            
            self.scheduler.start()
            self.is_running = True
            
            logger.info("Планировщик контента запущен")
            
        except Exception as e:
            logger.error(f"Ошибка запуска планировщика: {e}")
            raise
    
    async def stop(self):
        """Остановка планировщика"""
        try:
            logger.info("Остановка планировщика контента...")
            
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)
            
            self.is_running = False
            logger.info("Планировщик контента остановлен")
            
        except Exception as e:
            logger.error(f"Ошибка остановки планировщика: {e}")
    
    async def _check_scheduled_posts(self):
        """Проверить посты для публикации по расписанию"""
        try:
            async with self.db_session_factory() as db:
                scheduled_posts = await PostCRUD.get_scheduled_posts(db)
                
                for post in scheduled_posts:
                    try:
                        # Публикуем пост
                        success, message = await self.telegram_bot.publish_post(
                            post.id, 
                            post.target_channel_id
                        )
                        
                        if success:
                            logger.info(f"Автоматически опубликован пост #{post.id}")
                        else:
                            logger.error(f"Ошибка автопубликации поста #{post.id}: {message}")
                            
                            await LogCRUD.add_log(
                                db=db,
                                level="ERROR",
                                message=f"Ошибка автопубликации: {message}",
                                module="scheduler",
                                post_id=post.id
                            )
                    
                    except Exception as e:
                        logger.error(f"Ошибка публикации поста #{post.id}: {e}")
                        
                        await LogCRUD.add_log(
                            db=db,
                            level="ERROR",
                            message=f"Ошибка публикации: {e}",
                            module="scheduler",
                            post_id=post.id
                        )
        
        except Exception as e:
            logger.error(f"Ошибка проверки запланированных постов: {e}")
    
    async def _cleanup_old_logs(self):
        """Очистка старых логов (старше 30 дней)"""
        try:
            async with self.db_session_factory() as db:
                cutoff_date = datetime.now() - timedelta(days=30)
                
                # Здесь можно добавить логику очистки старых логов
                # Пока просто логируем
                logger.info("Выполнена очистка старых логов")
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message="Выполнена очистка старых логов",
                    module="scheduler"
                )
        
        except Exception as e:
            logger.error(f"Ошибка очистки логов: {e}")
    
    def schedule_post(self, post_id: int, target_channel_id: int, publish_time: datetime):
        """Запланировать публикацию поста"""
        try:
            job_id = f"publish_post_{post_id}"
            
            # Удаляем существующую задачу, если есть
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # Добавляем новую задачу
            self.scheduler.add_job(
                self._publish_scheduled_post,
                trigger="date",
                run_date=publish_time,
                args=[post_id, target_channel_id],
                id=job_id,
                name=f"Публикация поста #{post_id}"
            )
            
            logger.info(f"Пост #{post_id} запланирован к публикации на {publish_time}")
            
        except Exception as e:
            logger.error(f"Ошибка планирования поста {post_id}: {e}")
    
    async def _publish_scheduled_post(self, post_id: int, target_channel_id: int):
        """Опубликовать запланированный пост"""
        try:
            success, message = await self.telegram_bot.publish_post(post_id, target_channel_id)
            
            if success:
                logger.info(f"Запланированный пост #{post_id} опубликован")
            else:
                logger.error(f"Ошибка публикации запланированного поста #{post_id}: {message}")
        
        except Exception as e:
            logger.error(f"Ошибка публикации запланированного поста {post_id}: {e}")
    
    def cancel_scheduled_post(self, post_id: int):
        """Отменить запланированную публикацию поста"""
        try:
            job_id = f"publish_post_{post_id}"
            
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"Отменена запланированная публикация поста #{post_id}")
            
        except Exception as e:
            logger.error(f"Ошибка отмены публикации поста {post_id}: {e}")
    
    def get_scheduled_jobs(self) -> list:
        """Получить список запланированных задач"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                if job.id.startswith("publish_post_"):
                    jobs.append({
                        "id": job.id,
                        "name": job.name,
                        "next_run": job.next_run_time,
                        "args": job.args
                    })
            return jobs
        
        except Exception as e:
            logger.error(f"Ошибка получения списка задач: {e}")
            return []
    
    def add_interval_job(self, interval_hours: int = 2):
        """Добавить задачу публикации с интервалом"""
        try:
            # Удаляем существующую задачу интервальной публикации
            if self.scheduler.get_job("interval_publishing"):
                self.scheduler.remove_job("interval_publishing")
            
            # Добавляем новую задачу
            self.scheduler.add_job(
                self._interval_publishing,
                trigger=IntervalTrigger(hours=interval_hours),
                id="interval_publishing",
                name=f"Публикация каждые {interval_hours} часов"
            )
            
            logger.info(f"Настроена автопубликация каждые {interval_hours} часов")
            
        except Exception as e:
            logger.error(f"Ошибка настройки интервальной публикации: {e}")
    
    async def _interval_publishing(self):
        """Автоматическая публикация по интервалу"""
        try:
            async with self.db_session_factory() as db:
                # Получаем первый пост на модерации
                pending_posts = await PostCRUD.get_pending_posts(db)
                
                if not pending_posts:
                    logger.info("Нет постов для автопубликации")
                    return
                
                post = pending_posts[0]
                
                # Здесь можно добавить логику выбора целевого канала
                # Пока просто логируем
                logger.info(f"Пост #{post.id} готов к автопубликации")
                
        except Exception as e:
            logger.error(f"Ошибка интервальной публикации: {e}")
    
    def remove_interval_job(self):
        """Удалить задачу интервальной публикации"""
        try:
            if self.scheduler.get_job("interval_publishing"):
                self.scheduler.remove_job("interval_publishing")
                logger.info("Автопубликация по интервалу отключена")
        
        except Exception as e:
            logger.error(f"Ошибка отключения интервальной публикации: {e}")


# Глобальный экземпляр планировщика (будет инициализирован в main.py)
scheduler: Optional[ContentScheduler] = None
