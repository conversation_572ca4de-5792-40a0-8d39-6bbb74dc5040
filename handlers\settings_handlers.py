"""
Обработчики настроек для Content Monster GPT Bot
"""
from typing import Dict, Any, List
from datetime import datetime, time

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from loguru import logger

from config.settings import settings, REWRITE_PROMPTS
from database.crud import SettingsCRUD, LogCRUD


class SettingsHandlers:
    """Обработчики настроек"""
    
    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
    
    async def get_schedule_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру настроек расписания"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="⏰ Интервал публикации", callback_data="schedule_interval"),
                InlineKeyboardButton(text="🕐 Время публикации", callback_data="schedule_time")
            ],
            [
                InlineKeyboardButton(text="📊 Лимиты", callback_data="schedule_limits"),
                InlineKeyboardButton(text="🔄 Автопубликация", callback_data="schedule_auto")
            ],
            [
                InlineKeyboardButton(text="📅 Расписание на неделю", callback_data="schedule_weekly")
            ],
            [
                InlineKeyboardButton(text="🔙 Назад", callback_data="settings")
            ]
        ])
        return keyboard
    
    async def get_current_settings(self) -> Dict[str, Any]:
        """Получить текущие настройки"""
        async with self.db_session_factory() as db:
            try:
                settings_dict = {}
                
                # Основные настройки
                signature = await SettingsCRUD.get_setting(db, "default_signature")
                settings_dict["signature"] = signature or settings.default_signature
                
                # Настройки ИИ
                ai_model = await SettingsCRUD.get_setting(db, "ai_model")
                settings_dict["ai_model"] = ai_model or settings.openrouter_model
                
                ai_temperature = await SettingsCRUD.get_setting(db, "ai_temperature")
                settings_dict["ai_temperature"] = float(ai_temperature) if ai_temperature else settings.ai_temperature
                
                ai_max_tokens = await SettingsCRUD.get_setting(db, "ai_max_tokens")
                settings_dict["ai_max_tokens"] = int(ai_max_tokens) if ai_max_tokens else settings.ai_max_tokens
                
                # Настройки расписания
                post_interval = await SettingsCRUD.get_setting(db, "post_interval")
                settings_dict["post_interval"] = int(post_interval) if post_interval else settings.default_post_interval
                
                max_posts_per_hour = await SettingsCRUD.get_setting(db, "max_posts_per_hour")
                settings_dict["max_posts_per_hour"] = int(max_posts_per_hour) if max_posts_per_hour else settings.max_posts_per_hour
                
                # Настройки автопубликации
                auto_publish = await SettingsCRUD.get_setting(db, "auto_publish")
                settings_dict["auto_publish"] = auto_publish == "true" if auto_publish else False
                
                return settings_dict
                
            except Exception as e:
                logger.error(f"Ошибка получения настроек: {e}")
                return {}
    
    async def update_setting(self, key: str, value: str, description: str = None) -> bool:
        """Обновить настройку"""
        async with self.db_session_factory() as db:
            try:
                await SettingsCRUD.set_setting(db, key, value, description)
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message=f"Настройка изменена: {key} = {value}",
                    module="settings"
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Ошибка обновления настройки {key}: {e}")
                return False
    
    def format_settings_text(self, settings_dict: Dict[str, Any]) -> str:
        """Форматировать текст настроек"""
        text = "⚙️ <b>Текущие настройки</b>\n\n"
        
        # Основные настройки
        text += "<b>📝 Основные:</b>\n"
        signature = settings_dict.get("signature", "")
        text += f"• Подпись: {signature[:50]}{'...' if len(signature) > 50 else ''}\n\n"
        
        # Настройки ИИ
        text += "<b>🧠 Искусственный интеллект:</b>\n"
        text += f"• Модель: {settings_dict.get('ai_model', 'не задана')}\n"
        text += f"• Температура: {settings_dict.get('ai_temperature', 0.7)}\n"
        text += f"• Макс. токены: {settings_dict.get('ai_max_tokens', 2000)}\n\n"
        
        # Настройки расписания
        text += "<b>⏰ Расписание:</b>\n"
        interval = settings_dict.get("post_interval", 7200)
        hours = interval // 3600
        minutes = (interval % 3600) // 60
        text += f"• Интервал: {hours}ч {minutes}м\n"
        text += f"• Макс. в час: {settings_dict.get('max_posts_per_hour', 10)}\n"
        text += f"• Автопубликация: {'✅' if settings_dict.get('auto_publish', False) else '❌'}\n"
        
        return text
    
    def get_interval_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру выбора интервала"""
        intervals = [
            ("30 мин", 1800),
            ("1 час", 3600),
            ("2 часа", 7200),
            ("4 часа", 14400),
            ("6 часов", 21600),
            ("12 часов", 43200),
            ("24 часа", 86400)
        ]
        
        keyboard_buttons = []
        for text, seconds in intervals:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_interval:{seconds}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="schedule_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    def get_time_slots_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру временных слотов"""
        time_slots = [
            ("🌅 Утром (8:00)", "08:00"),
            ("🌞 Днем (12:00)", "12:00"),
            ("🌆 Вечером (18:00)", "18:00"),
            ("🌙 Ночью (22:00)", "22:00"),
            ("🕐 Каждые 3 часа", "every_3h"),
            ("🕕 Каждые 6 часов", "every_6h")
        ]
        
        keyboard_buttons = []
        for text, time_code in time_slots:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_time:{time_code}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="schedule_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    def get_limits_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру лимитов"""
        limits = [
            ("5 в час", 5),
            ("10 в час", 10),
            ("15 в час", 15),
            ("20 в час", 20),
            ("Без лимита", 0)
        ]
        
        keyboard_buttons = []
        for text, limit in limits:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_limit:{limit}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="schedule_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    def get_prompt_types_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру типов промптов"""
        prompt_types = [
            ("📰 Новости", "news"),
            ("🎭 Развлечения", "entertainment"),
            ("📝 Обычный", "default")
        ]
        
        keyboard_buttons = []
        for text, prompt_type in prompt_types:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_prompt:{prompt_type}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="ai_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    def get_temperature_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру температуры"""
        temperatures = [
            ("🧊 Консервативно (0.3)", 0.3),
            ("❄️ Сбалансированно (0.5)", 0.5),
            ("🌡️ Стандартно (0.7)", 0.7),
            ("🔥 Креативно (0.9)", 0.9),
            ("🌋 Очень креативно (1.2)", 1.2)
        ]
        
        keyboard_buttons = []
        for text, temp in temperatures:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_temp:{temp}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="ai_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    def get_max_tokens_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру максимальных токенов"""
        token_limits = [
            ("📝 Короткие (500)", 500),
            ("📄 Средние (1000)", 1000),
            ("📋 Длинные (2000)", 2000),
            ("📚 Очень длинные (4000)", 4000)
        ]
        
        keyboard_buttons = []
        for text, tokens in token_limits:
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=text,
                    callback_data=f"set_tokens:{tokens}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔙 Назад", callback_data="ai_settings")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    async def setup_weekly_schedule(self, schedule_data: Dict[str, List[str]]) -> bool:
        """Настроить недельное расписание"""
        async with self.db_session_factory() as db:
            try:
                import json
                
                await SettingsCRUD.set_setting(
                    db=db,
                    key="weekly_schedule",
                    value=json.dumps(schedule_data),
                    description="Расписание публикации по дням недели"
                )
                
                await LogCRUD.add_log(
                    db=db,
                    level="INFO",
                    message="Настроено недельное расписание",
                    module="settings"
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Ошибка настройки недельного расписания: {e}")
                return False
    
    async def get_weekly_schedule(self) -> Dict[str, List[str]]:
        """Получить недельное расписание"""
        async with self.db_session_factory() as db:
            try:
                import json
                
                schedule_json = await SettingsCRUD.get_setting(db, "weekly_schedule")
                
                if schedule_json:
                    return json.loads(schedule_json)
                else:
                    # Расписание по умолчанию
                    return {
                        "monday": ["09:00", "15:00", "21:00"],
                        "tuesday": ["09:00", "15:00", "21:00"],
                        "wednesday": ["09:00", "15:00", "21:00"],
                        "thursday": ["09:00", "15:00", "21:00"],
                        "friday": ["09:00", "15:00", "21:00"],
                        "saturday": ["10:00", "16:00"],
                        "sunday": ["10:00", "16:00"]
                    }
                    
            except Exception as e:
                logger.error(f"Ошибка получения недельного расписания: {e}")
                return {}
    
    def format_weekly_schedule(self, schedule: Dict[str, List[str]]) -> str:
        """Форматировать недельное расписание"""
        days_ru = {
            "monday": "Понедельник",
            "tuesday": "Вторник", 
            "wednesday": "Среда",
            "thursday": "Четверг",
            "friday": "Пятница",
            "saturday": "Суббота",
            "sunday": "Воскресенье"
        }
        
        text = "📅 <b>Расписание на неделю</b>\n\n"
        
        for day_en, times in schedule.items():
            day_ru = days_ru.get(day_en, day_en)
            times_str = ", ".join(times) if times else "Не запланировано"
            text += f"<b>{day_ru}:</b> {times_str}\n"
        
        return text


# Глобальный экземпляр обработчиков настроек
settings_handlers: SettingsHandlers = None
