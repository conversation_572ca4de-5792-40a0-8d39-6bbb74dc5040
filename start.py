#!/usr/bin/env python3
"""
Быстрый запуск Content Monster GPT Bot
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Проверить версию Python"""
    if sys.version_info < (3, 9):
        print("❌ Требуется Python 3.9 или выше!")
        print(f"Текущая версия: {sys.version}")
        return False
    return True


def check_env_file():
    """Проверить наличие .env файла"""
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ Файл .env не найден!")
        print("📝 Скопируйте .env.example в .env и заполните настройки:")
        print("   copy .env.example .env    # Windows")
        print("   cp .env.example .env      # Linux/macOS")
        return False
    return True


def check_dependencies():
    """Проверить установленные зависимости"""
    required_packages = [
        "aiogram",
        "pyrogram", 
        "sqlalchemy",
        "loguru",
        "pydantic",
        "aiohttp"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Не установлены пакеты: {', '.join(missing_packages)}")
        print("📦 Установите зависимости:")
        print("   pip install -r requirements.txt")
        return False
    
    return True


def install_dependencies():
    """Установить зависимости"""
    print("📦 Установка зависимостей...")
    
    try:
        # Основные зависимости без проблемных пакетов
        packages = [
            "aiogram>=3.4.0",
            "pyrogram>=2.0.100", 
            "sqlalchemy>=2.0.0",
            "alembic>=1.13.0",
            "aiosqlite>=0.19.0",
            "aiohttp>=3.9.0",
            "httpx>=0.26.0",
            "python-dotenv>=1.0.0",
            "pydantic>=2.5.0",
            "pydantic-settings>=2.1.0",
            "apscheduler>=3.10.0",
            "loguru>=0.7.0",
            "python-dateutil>=2.8.0",
            "asyncio-throttle>=1.0.0"
        ]
        
        for package in packages:
            print(f"Установка {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"⚠️ Ошибка установки {package}: {result.stderr}")
        
        print("✅ Основные зависимости установлены!")
        
        # Попытка установить опциональные пакеты
        optional_packages = ["tgcrypto", "pillow"]
        
        for package in optional_packages:
            print(f"Попытка установки {package} (опционально)...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package} установлен")
            else:
                print(f"⚠️ {package} не установлен (не критично)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка установки зависимостей: {e}")
        return False


def create_env_file():
    """Создать .env файл из примера"""
    try:
        if Path(".env.example").exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Файл .env создан из .env.example")
            print("📝 Отредактируйте .env файл и заполните настройки")
            return True
        else:
            print("❌ Файл .env.example не найден")
            return False
    except Exception as e:
        print(f"❌ Ошибка создания .env файла: {e}")
        return False


def main():
    """Главная функция"""
    print("🤖 Content Monster GPT Bot - Быстрый запуск")
    print("=" * 50)
    
    # Проверяем версию Python
    if not check_python_version():
        return 1
    
    # Проверяем .env файл
    if not check_env_file():
        response = input("Создать .env файл из примера? (y/n): ")
        if response.lower() in ['y', 'yes', 'да']:
            if not create_env_file():
                return 1
        else:
            return 1
    
    # Проверяем зависимости
    if not check_dependencies():
        response = input("Установить зависимости? (y/n): ")
        if response.lower() in ['y', 'yes', 'да']:
            if not install_dependencies():
                return 1
        else:
            print("❌ Установите зависимости вручную:")
            print("   pip install -r requirements.txt")
            return 1
    
    print("✅ Все проверки пройдены!")
    print("🚀 Запуск Content Monster GPT Bot...")
    print("=" * 50)
    
    # Запускаем основное приложение
    try:
        import main
        import asyncio
        asyncio.run(main.main())
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"💥 Ошибка запуска: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
