"""
Модели базы данных для Content Monster GPT Bot
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean,
    ForeignKey, JSON, Float, BigInteger
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class DonorChannel(Base):
    """Модель канала-донора"""
    __tablename__ = "donor_channels"

    id = Column(Integer, primary_key=True, index=True)
    channel_id = Column(BigInteger, unique=True, index=True, nullable=False)
    username = Column(String(255), nullable=True)
    title = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    last_post_id = Column(BigInteger, default=0)
    added_at = Column(DateTime, default=func.now())

    # Связи
    posts = relationship("Post", back_populates="donor_channel")


class TargetChannel(Base):
    """Модель целевого канала для публикации"""
    __tablename__ = "target_channels"

    id = Column(Integer, primary_key=True, index=True)
    channel_id = Column(BigInteger, unique=True, index=True, nullable=False)
    username = Column(String(255), nullable=True)
    title = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    added_at = Column(DateTime, default=func.now())

    # Индивидуальные настройки канала
    custom_signature = Column(Text, nullable=True)  # Индивидуальная подпись
    ai_prompt_type = Column(String(50), default="default")  # Тип промпта для ИИ
    post_interval = Column(Integer, nullable=True)  # Интервал публикации (секунды)
    max_posts_per_day = Column(Integer, default=10)  # Лимит постов в день

    # Связи
    posts = relationship("Post", back_populates="target_channel")
    donor_mappings = relationship("ChannelMapping", back_populates="target_channel")


class ChannelMapping(Base):
    """Модель связи донор-канала с целевым каналом"""
    __tablename__ = "channel_mappings"

    id = Column(Integer, primary_key=True, index=True)
    donor_channel_id = Column(Integer, ForeignKey("donor_channels.id"), nullable=False)
    target_channel_id = Column(Integer, ForeignKey("target_channels.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())

    # Индивидуальные настройки для этой связки
    custom_prompt = Column(Text, nullable=True)  # Кастомный промпт для этой связки
    priority = Column(Integer, default=1)  # Приоритет (1-высший, 10-низший)

    # Связи
    donor_channel = relationship("DonorChannel")
    target_channel = relationship("TargetChannel", back_populates="donor_mappings")


class Post(Base):
    """Модель поста"""
    __tablename__ = "posts"

    id = Column(Integer, primary_key=True, index=True)

    # Связи с каналами
    donor_channel_id = Column(Integer, ForeignKey("donor_channels.id"), nullable=False)
    target_channel_id = Column(Integer, ForeignKey("target_channels.id"), nullable=True)

    # Данные оригинального поста
    original_message_id = Column(BigInteger, nullable=False)
    original_text = Column(Text, nullable=True)
    original_media = Column(JSON, nullable=True)  # Список медиа файлов

    # Обработанные данные
    rewritten_text = Column(Text, nullable=True)
    final_text = Column(Text, nullable=True)  # С подписью

    # Статус обработки
    status = Column(String(50), default="pending")  # pending, approved, rejected, published

    # Временные метки
    created_at = Column(DateTime, default=func.now())
    processed_at = Column(DateTime, nullable=True)
    published_at = Column(DateTime, nullable=True)
    scheduled_for = Column(DateTime, nullable=True)

    # Метаданные
    ai_model_used = Column(String(100), nullable=True)
    processing_time = Column(Float, nullable=True)  # Время обработки в секундах

    # Связи
    donor_channel = relationship("DonorChannel", back_populates="posts")
    target_channel = relationship("TargetChannel", back_populates="posts")


class BlacklistWord(Base):
    """Модель слов черного списка"""
    __tablename__ = "blacklist_words"

    id = Column(Integer, primary_key=True, index=True)
    word = Column(String(255), unique=True, index=True, nullable=False)
    is_active = Column(Boolean, default=True)
    added_at = Column(DateTime, default=func.now())


class BotSettings(Base):
    """Модель настроек бота"""
    __tablename__ = "bot_settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True, nullable=False)
    value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class UserSession(Base):
    """Модель пользовательских сессий"""
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(BigInteger, unique=True, index=True, nullable=False)
    current_action = Column(String(100), nullable=True)
    session_data = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class LogEntry(Base):
    """Модель логов"""
    __tablename__ = "log_entries"

    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR
    message = Column(Text, nullable=False)
    module = Column(String(100), nullable=True)
    user_id = Column(BigInteger, nullable=True)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())

    # Связи
    post = relationship("Post")
