"""
Дополнительные обработчики для админ-панели
"""
from typing import List, Dict, Any
from datetime import datetime, timedelta

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from loguru import logger

from database.crud import (
    PostCRUD, DonorChannelCRUD, TargetChannelCRUD, 
    BlacklistCRUD, SettingsCRUD, LogCRUD
)
from utils.media_handler import media_handler


class AdminHandlers:
    """Дополнительные обработчики для админ-панели"""
    
    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
    
    async def get_statistics_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру статистики"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📊 Общая статистика", callback_data="stats_general"),
                InlineKeyboardButton(text="📈 Статистика постов", callback_data="stats_posts")
            ],
            [
                InlineKeyboardButton(text="⏱ За сегодня", callback_data="stats_today"),
                InlineKeyboardButton(text="📅 За неделю", callback_data="stats_week")
            ],
            [
                InlineKeyboardButton(text="🔙 Назад", callback_data="main_menu")
            ]
        ])
        return keyboard
    
    async def get_general_statistics(self) -> str:
        """Получить общую статистику"""
        async with self.db_session_factory() as db:
            try:
                # Каналы
                donor_channels = await DonorChannelCRUD.get_all_active(db)
                target_channels = await TargetChannelCRUD.get_all_active(db)
                
                # Посты
                pending_posts = await PostCRUD.get_pending_posts(db)
                
                # Черный список
                blacklist_words = await BlacklistCRUD.get_all_active(db)
                
                # Статистика постов за все время
                from sqlalchemy import select, func
                from database.models import Post
                
                # Общее количество постов
                total_posts_result = await db.execute(
                    select(func.count(Post.id))
                )
                total_posts = total_posts_result.scalar() or 0
                
                # Опубликованные посты
                published_posts_result = await db.execute(
                    select(func.count(Post.id)).where(Post.status == "published")
                )
                published_posts = published_posts_result.scalar() or 0
                
                # Отклоненные посты
                rejected_posts_result = await db.execute(
                    select(func.count(Post.id)).where(Post.status == "rejected")
                )
                rejected_posts = rejected_posts_result.scalar() or 0
                
                text = (
                    "📊 <b>Общая статистика</b>\n\n"
                    f"📥 Каналы-доноры: {len(donor_channels)}\n"
                    f"📤 Целевые каналы: {len(target_channels)}\n\n"
                    f"📝 Всего постов: {total_posts}\n"
                    f"✅ Опубликовано: {published_posts}\n"
                    f"⏳ На модерации: {len(pending_posts)}\n"
                    f"❌ Отклонено: {rejected_posts}\n\n"
                    f"🚫 Слов в черном списке: {len(blacklist_words)}\n\n"
                    f"📈 Процент публикации: {(published_posts / max(total_posts, 1) * 100):.1f}%"
                )
                
                return text
                
            except Exception as e:
                logger.error(f"Ошибка получения статистики: {e}")
                return "❌ Ошибка получения статистики"
    
    async def get_posts_statistics(self) -> str:
        """Получить статистику постов"""
        async with self.db_session_factory() as db:
            try:
                from sqlalchemy import select, func, desc
                from database.models import Post, DonorChannel
                
                # Статистика по статусам
                status_stats = {}
                for status in ["pending", "approved", "published", "rejected", "error"]:
                    result = await db.execute(
                        select(func.count(Post.id)).where(Post.status == status)
                    )
                    status_stats[status] = result.scalar() or 0
                
                # Топ каналов-доноров по количеству постов
                top_donors_result = await db.execute(
                    select(
                        DonorChannel.title,
                        func.count(Post.id).label('post_count')
                    )
                    .join(Post, DonorChannel.id == Post.donor_channel_id)
                    .group_by(DonorChannel.id, DonorChannel.title)
                    .order_by(desc('post_count'))
                    .limit(5)
                )
                top_donors = top_donors_result.all()
                
                # Среднее время обработки ИИ
                avg_processing_time_result = await db.execute(
                    select(func.avg(Post.processing_time))
                    .where(Post.processing_time.isnot(None))
                )
                avg_processing_time = avg_processing_time_result.scalar() or 0
                
                text = (
                    "📈 <b>Статистика постов</b>\n\n"
                    f"⏳ На модерации: {status_stats['pending']}\n"
                    f"✅ Одобрено: {status_stats['approved']}\n"
                    f"📤 Опубликовано: {status_stats['published']}\n"
                    f"❌ Отклонено: {status_stats['rejected']}\n"
                    f"⚠️ Ошибки: {status_stats['error']}\n\n"
                    f"🧠 Среднее время ИИ: {avg_processing_time:.2f} сек\n\n"
                    "<b>🏆 Топ каналов-доноров:</b>\n"
                )
                
                for i, (title, count) in enumerate(top_donors, 1):
                    text += f"{i}. {title}: {count} постов\n"
                
                if not top_donors:
                    text += "Нет данных\n"
                
                return text
                
            except Exception as e:
                logger.error(f"Ошибка получения статистики постов: {e}")
                return "❌ Ошибка получения статистики постов"
    
    async def get_today_statistics(self) -> str:
        """Получить статистику за сегодня"""
        async with self.db_session_factory() as db:
            try:
                from sqlalchemy import select, func
                from database.models import Post
                
                today = datetime.now().date()
                tomorrow = today + timedelta(days=1)
                
                # Посты за сегодня
                today_posts_result = await db.execute(
                    select(func.count(Post.id))
                    .where(
                        Post.created_at >= today,
                        Post.created_at < tomorrow
                    )
                )
                today_posts = today_posts_result.scalar() or 0
                
                # Опубликованные за сегодня
                published_today_result = await db.execute(
                    select(func.count(Post.id))
                    .where(
                        Post.published_at >= today,
                        Post.published_at < tomorrow
                    )
                )
                published_today = published_today_result.scalar() or 0
                
                # Отклоненные за сегодня
                rejected_today_result = await db.execute(
                    select(func.count(Post.id))
                    .where(
                        Post.status == "rejected",
                        Post.processed_at >= today,
                        Post.processed_at < tomorrow
                    )
                )
                rejected_today = rejected_today_result.scalar() or 0
                
                text = (
                    f"⏱ <b>Статистика за {today.strftime('%d.%m.%Y')}</b>\n\n"
                    f"📝 Новых постов: {today_posts}\n"
                    f"📤 Опубликовано: {published_today}\n"
                    f"❌ Отклонено: {rejected_today}\n\n"
                )
                
                if today_posts > 0:
                    success_rate = (published_today / today_posts) * 100
                    text += f"📈 Процент публикации: {success_rate:.1f}%"
                else:
                    text += "📈 Процент публикации: 0%"
                
                return text
                
            except Exception as e:
                logger.error(f"Ошибка получения статистики за сегодня: {e}")
                return "❌ Ошибка получения статистики за сегодня"
    
    async def get_week_statistics(self) -> str:
        """Получить статистику за неделю"""
        async with self.db_session_factory() as db:
            try:
                from sqlalchemy import select, func
                from database.models import Post
                
                week_ago = datetime.now() - timedelta(days=7)
                
                # Посты за неделю
                week_posts_result = await db.execute(
                    select(func.count(Post.id))
                    .where(Post.created_at >= week_ago)
                )
                week_posts = week_posts_result.scalar() or 0
                
                # Опубликованные за неделю
                published_week_result = await db.execute(
                    select(func.count(Post.id))
                    .where(Post.published_at >= week_ago)
                )
                published_week = published_week_result.scalar() or 0
                
                # Отклоненные за неделю
                rejected_week_result = await db.execute(
                    select(func.count(Post.id))
                    .where(
                        Post.status == "rejected",
                        Post.processed_at >= week_ago
                    )
                )
                rejected_week = rejected_week_result.scalar() or 0
                
                # Среднее количество постов в день
                avg_posts_per_day = week_posts / 7
                
                text = (
                    "📅 <b>Статистика за неделю</b>\n\n"
                    f"📝 Новых постов: {week_posts}\n"
                    f"📤 Опубликовано: {published_week}\n"
                    f"❌ Отклонено: {rejected_week}\n\n"
                    f"📊 В среднем в день: {avg_posts_per_day:.1f} постов\n\n"
                )
                
                if week_posts > 0:
                    success_rate = (published_week / week_posts) * 100
                    text += f"📈 Процент публикации: {success_rate:.1f}%"
                else:
                    text += "📈 Процент публикации: 0%"
                
                return text
                
            except Exception as e:
                logger.error(f"Ошибка получения статистики за неделю: {e}")
                return "❌ Ошибка получения статистики за неделю"
    
    async def get_ai_settings_keyboard(self) -> InlineKeyboardMarkup:
        """Получить клавиатуру настроек ИИ"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🤖 Сменить модель", callback_data="ai_change_model"),
                InlineKeyboardButton(text="🌡 Температура", callback_data="ai_temperature")
            ],
            [
                InlineKeyboardButton(text="📏 Макс. токены", callback_data="ai_max_tokens"),
                InlineKeyboardButton(text="📝 Тип промпта", callback_data="ai_prompt_type")
            ],
            [
                InlineKeyboardButton(text="🧪 Тест API", callback_data="ai_test"),
                InlineKeyboardButton(text="💰 Баланс", callback_data="ai_balance")
            ],
            [
                InlineKeyboardButton(text="🔙 Назад", callback_data="settings")
            ]
        ])
        return keyboard
    
    async def get_available_models(self) -> List[str]:
        """Получить список доступных моделей ИИ"""
        from core.openrouter_api import openrouter_api
        
        try:
            async with openrouter_api:
                models = await openrouter_api.get_available_models()
                
                # Фильтруем популярные модели
                popular_models = [
                    "openai/gpt-4-turbo-preview",
                    "openai/gpt-4",
                    "openai/gpt-3.5-turbo",
                    "anthropic/claude-3-sonnet",
                    "anthropic/claude-3-haiku",
                    "mistralai/mistral-7b-instruct",
                    "meta-llama/llama-2-70b-chat"
                ]
                
                # Возвращаем только те, что доступны
                available_popular = [model for model in popular_models if model in models]
                
                return available_popular if available_popular else models[:10]
                
        except Exception as e:
            logger.error(f"Ошибка получения списка моделей: {e}")
            return [
                "openai/gpt-4-turbo-preview",
                "openai/gpt-3.5-turbo",
                "anthropic/claude-3-sonnet"
            ]
    
    async def test_ai_api(self) -> str:
        """Протестировать API ИИ"""
        from core.openrouter_api import openrouter_api
        
        try:
            async with openrouter_api:
                test_text = "Привет! Это тестовое сообщение для проверки API."
                rewritten_text, processing_time = await openrouter_api.rewrite_text(
                    text=test_text,
                    prompt_type="default"
                )
                
                return (
                    "✅ <b>Тест API успешен!</b>\n\n"
                    f"🕐 Время обработки: {processing_time:.2f} сек\n"
                    f"🤖 Модель: {openrouter_api.model}\n\n"
                    f"<b>Исходный текст:</b>\n{test_text}\n\n"
                    f"<b>Результат:</b>\n{rewritten_text}"
                )
                
        except Exception as e:
            logger.error(f"Ошибка тестирования API: {e}")
            return f"❌ <b>Ошибка тестирования API:</b>\n{e}"
    
    async def cleanup_old_data(self, days: int = 30) -> str:
        """Очистить старые данные"""
        async with self.db_session_factory() as db:
            try:
                from sqlalchemy import delete
                from database.models import LogEntry, Post
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Удаляем старые логи
                logs_deleted = await db.execute(
                    delete(LogEntry).where(LogEntry.created_at < cutoff_date)
                )
                
                # Удаляем старые отклоненные посты
                posts_deleted = await db.execute(
                    delete(Post).where(
                        Post.status == "rejected",
                        Post.created_at < cutoff_date
                    )
                )
                
                await db.commit()
                
                # Очищаем медиа файлы
                media_handler.cleanup_old_files(days * 24)
                
                return (
                    f"✅ <b>Очистка завершена!</b>\n\n"
                    f"🗑 Удалено логов: {logs_deleted.rowcount}\n"
                    f"📝 Удалено постов: {posts_deleted.rowcount}\n"
                    f"📁 Очищены медиа файлы старше {days} дней"
                )
                
            except Exception as e:
                logger.error(f"Ошибка очистки данных: {e}")
                return f"❌ Ошибка очистки данных: {e}"


# Глобальный экземпляр обработчиков
admin_handlers: AdminHandlers = None
