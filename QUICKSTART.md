# ⚡ Быстрый старт Content Monster GPT Bot

## 🚀 За 5 минут до запуска!

### 1. Скачайте и распакуйте проект
```bash
# Скачайте архив проекта и распакуйте в папку
cd content-monster-gpt
```

### 2. Запустите автоматическую установку
```bash
python start.py
```

Скрипт автоматически:
- ✅ Проверит версию Python
- ✅ Создаст .env файл
- ✅ Установит зависимости
- ✅ Запустит бота

### 3. Получите необходимые токены

#### 🤖 Telegram Bot Token
1. Напишите [@BotFather](https://t.me/BotFather)
2. Отправьте `/newbot`
3. Следуйте инструкциям
4. Скопируйте токен

#### 🔑 API_ID и API_HASH
1. Перейдите на [my.telegram.org](https://my.telegram.org)
2. Войдите в аккаунт
3. Создайте приложение
4. Скопируйте api_id и api_hash

#### 🧠 OpenRouter API Key
1. Зарегистрируйтесь на [openrouter.ai](https://openrouter.ai)
2. Пополните баланс ($5-10)
3. Создайте API ключ
4. Скопируйте ключ

#### 👤 Admin ID
1. Напишите [@userinfobot](https://t.me/userinfobot)
2. Скопируйте ваш User ID

### 4. Заполните .env файл

Откройте файл `.env` и заполните:

```env
# Замените на ваши данные
BOT_TOKEN=1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
API_ID=12345678
API_HASH=abcdef1234567890abcdef1234567890
OPENROUTER_API_KEY=sk-or-v1-abcdef1234567890abcdef1234567890
ADMIN_ID=123456789
```

### 5. Запустите бота
```bash
python main.py
```

При первом запуске:
1. Введите номер телефона
2. Введите код из Telegram
3. При необходимости введите пароль 2FA

### 6. Настройте каналы

В Telegram напишите боту:
1. `/start` - Приветствие
2. `/menu` - Главное меню
3. Добавьте каналы-доноры
4. Добавьте целевые каналы

## 🎯 Готово!

Ваш Content Monster GPT Bot готов к работе!

### Что дальше?

1. **Добавьте каналы-доноры** - источники контента
2. **Настройте целевые каналы** - куда публиковать
3. **Настройте черный список** - нежелательные слова
4. **Протестируйте модерацию** - одобрите первые посты
5. **Настройте расписание** - автоматическую публикацию

### Полезные команды:

- `/menu` - Главное меню
- `/status` - Статус системы
- `/logs` - Просмотр логов

### Если что-то не работает:

1. Проверьте логи в папке `logs/`
2. Убедитесь в правильности токенов
3. Проверьте интернет-соединение
4. Прочитайте [FAQ.md](FAQ.md)

---

**Удачи с автоматизацией контента!** 🚀
