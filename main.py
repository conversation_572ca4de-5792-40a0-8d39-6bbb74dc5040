"""
Content Monster GPT Bot - Главный файл запуска
"""
import asyncio
import signal
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Импортируем компоненты после загрузки .env
from config.settings import settings
from database.database import db_manager
from database.crud import BlacklistCRUD
from core.userbot import ContentMonsterUserbot
from core.telegram_bot import ContentMonsterBot
from core.scheduler import ContentScheduler
from core.openrouter_api import openrouter_api
from utils.filters import content_filter
from utils.logger import setup_logging


class ContentMonsterApp:
    """Главный класс приложения"""
    
    def __init__(self):
        self.userbot = None
        self.telegram_bot = None
        self.scheduler = None
        self.is_running = False
    
    async def start(self):
        """Запуск всех компонентов системы"""
        try:
            logger.info("🚀 Запуск Content Monster GPT Bot...")
            
            # Инициализируем базу данных
            await db_manager.init_database()
            
            # Загружаем черный список в фильтр
            await self._load_blacklist()
            
            # Проверяем API OpenRouter
            await self._check_openrouter_api()
            
            # Инициализируем компоненты
            self.userbot = ContentMonsterUserbot(db_manager.get_session)
            self.telegram_bot = ContentMonsterBot(db_manager.get_session)
            self.scheduler = ContentScheduler(db_manager.get_session, self.telegram_bot)
            
            # Запускаем компоненты
            await self.userbot.start()
            await self.scheduler.start()
            
            # Устанавливаем глобальные экземпляры
            import core.userbot
            import core.telegram_bot
            import core.scheduler
            
            core.userbot.userbot = self.userbot
            core.telegram_bot.telegram_bot = self.telegram_bot
            core.scheduler.scheduler = self.scheduler
            
            self.is_running = True
            
            logger.info("✅ Content Monster GPT Bot успешно запущен!")
            logger.info(f"👤 Админ ID: {settings.admin_id}")
            logger.info(f"🧠 ИИ модель: {settings.openrouter_model}")
            logger.info(f"📊 Максимум каналов-доноров: {settings.max_donor_channels}")
            
            # Запускаем Telegram Bot (блокирующий вызов)
            await self.telegram_bot.start()
            
        except Exception as e:
            logger.error(f"❌ Ошибка запуска приложения: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """Остановка всех компонентов системы"""
        try:
            logger.info("🛑 Остановка Content Monster GPT Bot...")
            
            self.is_running = False
            
            # Останавливаем компоненты
            if self.scheduler:
                await self.scheduler.stop()
            
            if self.userbot:
                await self.userbot.stop()
            
            if self.telegram_bot:
                await self.telegram_bot.stop()
            
            # Закрываем OpenRouter API
            await openrouter_api.close()
            
            # Закрываем базу данных
            await db_manager.close_database()
            
            logger.info("✅ Content Monster GPT Bot остановлен")
            
        except Exception as e:
            logger.error(f"❌ Ошибка остановки приложения: {e}")
    
    async def _load_blacklist(self):
        """Загрузить черный список в фильтр"""
        try:
            async with db_manager.get_session() as db:
                blacklist_words = await BlacklistCRUD.get_all_active(db)
                content_filter.update_blacklist(blacklist_words)
                logger.info(f"Загружено {len(blacklist_words)} слов в черный список")
        
        except Exception as e:
            logger.warning(f"Ошибка загрузки черного списка: {e}")
    
    async def _check_openrouter_api(self):
        """Проверить доступность OpenRouter API"""
        try:
            logger.info("Проверка OpenRouter API...")
            
            async with openrouter_api:
                is_available = await openrouter_api.check_api_status()
                
                if is_available:
                    logger.info("✅ OpenRouter API доступен")
                else:
                    logger.warning("⚠️ OpenRouter API недоступен")
        
        except Exception as e:
            logger.error(f"❌ Ошибка проверки OpenRouter API: {e}")
            raise


# Глобальный экземпляр приложения
app = ContentMonsterApp()


async def main():
    """Главная функция"""
    
    def signal_handler(signum, frame):
        """Обработчик сигналов для корректного завершения"""
        logger.info(f"Получен сигнал {signum}, завершение работы...")
        asyncio.create_task(app.stop())
    
    # Регистрируем обработчики сигналов
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await app.start()
    
    except KeyboardInterrupt:
        logger.info("Получен сигнал прерывания")
    
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        sys.exit(1)
    
    finally:
        await app.stop()


if __name__ == "__main__":
    # Проверяем наличие .env файла
    if not Path(".env").exists():
        print("❌ Файл .env не найден!")
        print("📝 Скопируйте .env.example в .env и заполните настройки")
        sys.exit(1)
    
    # Проверяем обязательные настройки
    required_settings = [
        "BOT_TOKEN",
        "API_ID", 
        "API_HASH",
        "OPENROUTER_API_KEY",
        "ADMIN_ID"
    ]
    
    missing_settings = []
    for setting in required_settings:
        if not getattr(settings, setting.lower(), None):
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ Не заданы обязательные настройки: {', '.join(missing_settings)}")
        print("📝 Проверьте файл .env")
        sys.exit(1)
    
    # Запускаем приложение
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        sys.exit(1)
