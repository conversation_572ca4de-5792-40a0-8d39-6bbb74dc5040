#!/usr/bin/env python3
"""
Миграция базы данных для добавления новых полей
"""
import asyncio
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.append(str(Path(__file__).parent))

from dotenv import load_dotenv
load_dotenv()

from database.database import db_manager
from database.models import Base


async def migrate_database():
    """Миграция базы данных"""
    print("🔄 Начинаем миграцию базы данных...")

    try:
        # Инициализируем базу данных с новыми моделями
        await db_manager.init_database()
        print("✅ База данных успешно обновлена!")

        print("\n📋 Новые возможности:")
        print("• Индивидуальные подписи для каждого канала")
        print("• Связи донор-канал → целевой канал")
        print("• Настройки ИИ для каждого канала")
        print("• Приоритеты обработки контента")

    except Exception as e:
        print(f"❌ Ошибка миграции: {e}")
        return False

    return True


if __name__ == "__main__":
    success = asyncio.run(migrate_database())
    if success:
        print("\n🚀 Теперь можно запускать бота!")
    else:
        print("\n💥 Миграция не удалась!")
        sys.exit(1)
